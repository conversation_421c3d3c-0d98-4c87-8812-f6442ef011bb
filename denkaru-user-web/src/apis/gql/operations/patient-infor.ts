import { gql } from "@/apis/gql/apollo-client";

export const GET_INSURANCE_MST = gql`
  query getApiPatientInforGetInsuranceMst($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforGetInsuranceMst(ptId: $ptId, sinDate: $sinDate) {
      data {
        insuranceMst {
          hokenMstAlLData {
            futanKbn
            futanRate
            startDate
            endDate
            hokenNo
            hokenEdaNo
            hokenSName
            houbetu
            hokenSbtKbn
            checkDigit
            ageStart
            ageEnd
            isFutansyaNoCheck
            isJyukyusyaNoCheck
            jyuKyuCheckDigit
            isTokusyuNoCheck
            hokenName
            hokenNameCd
            hokenKohiKbn
            isOtherPrefValid
            receKisai
            isLimitList
            isLimitListSum
            enTen
            kaiLimitFutan
            dayLimitFutan
            monthLimitFutan
            monthLimitCount
            limitKbn
            countKbn
            futanYusen
            calcSpKbn
            monthSpLimit
            kogakuTekiyo
            kogakuTotalKbn
            kogakuHairyoKbn
            receSeikyuKbn
            receKisaiKokho
            receKisai2
            receTenKisai
            receFutanRound
            receZeroKisai
            receSpKbn
            prefactureName
            prefNo
            sortNo
            seikyuYm
            receFutanHide
            receFutanKbn
            kogakuTotalAll
            kogakuTotalExcFutan
            kaiFutangaku
            isAdded
            dayLimitCount
            excepHokenSyas {
              id
              hpId
              prefNo
              hokenNo
              hokenEdaNo
              startDate
              hokensyaNo
            }
            selectedValueMaster
            displayTextMaster
            displayHokenNo
            moneyLimitListFlag
            houbetuDisplayText
            displayTextMasterWithoutHokenNo
          }
        }
        prefNo
      }
    }
  }
`;

export const GET_TOKKI_MST_LIST = gql`
  query getApiPatientInforGetTokkiMstList($seikyuYm: Int) {
    getApiPatientInforGetTokkiMstList(seikyuYm: $seikyuYm) {
      data {
        tokkiMstList {
          tokkiCd
          tokkiName
          displayTextMst
        }
      }
    }
  }
`;

export const SEARCH_PATIENT_INFO_BY_PTNUM = gql`
  query getApiPatientInforSearchPatientInfoByPtNum(
    $ptNum: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforSearchPatientInfoByPtNum(
      ptNum: $ptNum
      sinDate: $sinDate
    ) {
      data {
        patientInfor {
          hpId
          ptId
          ptNum
          kanaName
          name
          seqNo
          referenceNo
          sex
          birthday
          limitConsFlg
          isDead
          deathDate
          homePost
          homeAddress1
          homeAddress2
          tel1
          tel2
          mail
          setanusi
          zokugara
          job
          renrakuName
          renrakuPost
          renrakuAddress1
          renrakuAddress2
          renrakuTel
          renrakuMemo
          officeName
          officePost
          officeAddress1
          officeAddress2
          officeTel
          officeMemo
          isRyosyoDetail
          primaryDoctor
          isTester
          mainHokenPid
          memo
          firstVisitDate
          rainCountInt
          rainCount
          comment
          lastVisitDate
          isShowKyuSeiName
          sinDate
          birthdayDisplay
          age
        }
      }
    }
  }
`;

export const GET_PATIENT_BY_ID = gql`
  query getApiPatientInforGetPatientById(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $listStatus: [Int!]
    $isShowKyuSeiName: Boolean
  ) {
    getApiPatientInforGetPatientById(
      ptId: $ptId
      sinDate: $sinDate
      raiinNo: $raiinNo
      listStatus: $listStatus
      isShowKyuSeiName: $isShowKyuSeiName
    ) {
      data {
        data {
          age
          birthday
          birthdayDisplay
          comment
          deathDate
          firstVisitDate
          homeAddress1
          homeAddress2
          homePost
          hpId
          isDead
          isRyosyoDetail
          isShowKyuSeiName
          isTester
          job
          kanaName
          lastVisitDate
          limitConsFlg
          mail
          mainHokenPid
          memo
          name
          officeAddress1
          officeAddress2
          officeMemo
          officeName
          officePost
          officeTel
          primaryDoctor
          ptId
          ptNum
          rainCount
          rainCountInt
          referenceNo
          renrakuAddress1
          renrakuAddress2
          renrakuMemo
          renrakuName
          renrakuPost
          renrakuTel
          seqNo
          setanusi
          sex
          sinDate
          tel1
          tel2
          zokugara
          renrakuTel2
          renrakuName2
          # portalCustomerId
        }
      }
    }
  }
`;

export const SEARCH_HOKENSYA_MST = gql`
  query getApiPatientInforGetHokenSyaMst($keyword: String, $sinDate: Int) {
    getApiPatientInforSearchHokensyaMst(keyword: $keyword, sinDate: $sinDate) {
      data {
        listData {
          hpId
          name
          kanaName
          houbetuKbn
          houbetu
          hokenKbn
          prefNo
          hokensyaNo
          kigo
          bango
          rateHonnin
          rateKazoku
          postCode
          address1
          address2
          tel1
          isKigoNa
          isReadOnlyHokenSyaNo
          postCdDisplay
        }
      }
    }
  }
`;

export const SIMPLE_PATIENT_SEARCH = gql`
  query getApiPatientInforSearchSimple(
    $keyword: String
    $isContainMode: Boolean
    $pageIndex: Int
    $pageSize: Int
    $sortData: [EmrCloudApiRequestsPatientInforSortColInput]
  ) {
    getApiPatientInforSearchSimple(
      keyword: $keyword
      isContainMode: $isContainMode
      pageIndex: $pageIndex
      pageSize: $pageSize
      sortData: $sortData
    ) {
      data {
        data {
          ptId
          ptNum
          kanaName
          name
          birthday
          birthdayRaw
          sex
          age
          tel1
          tel2
          renrakuTel
          homePost
          homeAddress
          lastVisitDate
          birthdayDisplay
        }
      }
    }
  }
`;

export const GET_PATIENT_INFO_BETWEEN_TIMES_LIST = gql`
  query getApiPatientInforGetPatientInfoBetweenTimesList(
    $simYm: Int
    $startDateD: Int
    $startTimeH: Int
    $startTimeM: Int
    $endDateD: Int
    $endTimeH: Int
    $endTimeM: Int
  ) {
    getApiPatientInforGetPatientInfoBetweenTimesList(
      sinYm: $simYm
      startDateD: $startDateD
      startTimeH: $startTimeH
      startTimeM: $startTimeM
      endDateD: $endDateD
      endTimeH: $endTimeH
      endTimeM: $endTimeM
    ) {
      data {
        patientInfoList {
          ptId
          ptNum
          kanaName
          name
          sex
        }
      }
    }
  }
`;

export const GET_INSURANCES_DATA_BY_PT_ID = gql`
  query getApiPatientInforGetInsurancesDataByPtId(
    $ptId: BigInt
    $sinDate: Int
    $isOdrInf: Boolean
  ) {
    getApiPatientInforGetHokenPatternByPtId(
      ptId: $ptId
      sinDate: $sinDate
      isOdrInf: $isOdrInf
    ) {
      data {
        data {
          patternName
          hokenKbn
          hokenPid
          usedPattern {
            hokenPid
            orderCount
          }
          isUsedPattern
          sinDate
          sinDateRecentUse
          hokenInf {
            bango
            checkDate
            edaNo
            endDate
            hokenNo
            hokenSName
            hokensyaNo
            kigo
            startDate
            hokenId
            onlineConfirmCheckDate
            isExpirated
            rousaiKofuNo
            jibaiHokenName
            hokenKbn
            seqNo
            hokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi1 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi2 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi3 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
          kohi4 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
            listHokenCheck {
              checkComment
              checkDate
              checkId
              checkMachine
              checkName
              hokenGrp
              hokenId
              isDeleted
              ptId
              seqNo
              onlineConfirmationId
            }
          }
        }
      }
    }

    getApiPatientInforGetHokenInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          bango
          edaNo
          endDate
          kigo
          startDate
          checkDate
          hokenSName
          hokensyaNo
          onlineConfirmCheckDate
          hokenNo
          hokenId
          isExpirated
          hokenSentaku
          hokenKbn
          rousaiKofuNo
          jibaiHokenName
          seqNo
          hokenCheck {
            checkComment
            checkDate
            checkId
            checkMachine
            checkName
            hokenGrp
            hokenId
            isDeleted
            ptId
            seqNo
            onlineConfirmationId
          }
          houbetu
        }
      }
    }

    getApiPatientInforGetKohiInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          checkDate
          sinDate
          endDate
          isEmptyModel
          kohiName
          houbetu
          futansyaNo
          jyukyusyaNo
          tokusyuNo
          onlineConfirmCheckDate
          hokenId
          isExpirated
          startDate
          prefNo
          hokenSbtKbn
          seqNo
          listHokenCheck {
            checkComment
            checkDate
            checkId
            checkMachine
            checkName
            hokenGrp
            hokenId
            isDeleted
            ptId
            seqNo
            onlineConfirmationId
          }
        }
      }
    }
  }
`;

export const GET_PT_KYUSEI_INF = gql`
  query getApiPatientInforGetPtKyuseiInf($ptId: BigInt, $isDeleted: Boolean) {
    getApiPatientInforGetPtKyuseiInf(isDeleted: $isDeleted, ptId: $ptId) {
      data {
        ptKyuseiInfModels {
          endDate
          kanaName
          name
          isDeleted
          seqNo
        }
      }
    }
  }
`;

export const POST_PT_KYUSEI_INF = gql`
  mutation postApiPatientInforSavePtKyusei(
    $emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput: EmrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput
  ) {
    postApiPatientInforSavePtKyusei(
      emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput: $emrCloudApiRequestsPatientInforPtKyuseiInfSavePtKyuseiRequestInput
    ) {
      message
    }
  }
`;

export const SAVE_PATIENT_INFO = gql`
  mutation postApiPatientInforSavePatient(
    $payload: EmrCloudApiRequestsPatientInforBasicPatientInfoSaveBasicPatientInfoRequestInput
  ) {
    postApiPatientInforSavePatient(
      emrCloudApiRequestsPatientInforBasicPatientInfoSaveBasicPatientInfoRequestInput: $payload
    ) {
      data {
        state
        ptID
        raiinNo
        validateDetails {
          code
          fieldName
          message
          listPtNum
          type
        }
      }
    }
  }
`;

export const VALIDATE_HOKEN_INSURANCE = gql`
  mutation postApiPatientInforValidateHoken(
    $payload: EmrCloudApiRequestsPatientInforValidateHokenRequestInput
  ) {
    postApiPatientInforValidateHoken(
      emrCloudApiRequestsPatientInforValidateHokenRequestInput: $payload
    ) {
      data {
        detail {
          displayTextMst
          endDate
          fieldName
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
    }
  }
`;

export const VALIDATE_HOKEN_PATTERN = gql`
  query getApiPatientInforValidateHokenPattern(
    $hokenId: Int
    $kohi2Id: Int
    $kohi1Id: Int
    $kohi3Id: Int
    $kohi4Id: Int
    $ptBirthday: Int
    $ptId: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforValidateHokenPattern(
      hokenId: $hokenId
      kohi2Id: $kohi2Id
      kohi1Id: $kohi1Id
      kohi3Id: $kohi3Id
      kohi4Id: $kohi4Id
      ptBirthday: $ptBirthday
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        detail {
          displayTextMst
          endDate
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
    }
  }
`;

export const GET_HOKEN_PATTERN_BY_PT_ID = gql`
  query getApiPatientInforGetHokenPatternByPtId($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforGetHokenPatternByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          patternName
          hokenKbn
          hokenPid
          sinDateRecentUse
          isDefault
          hokenInf {
            bango
            checkDate
            edaNo
            endDate
            hokenNo
            hokenSName
            hokensyaNo
            kigo
            startDate
            hokenId
            onlineConfirmCheckDate
            isExpirated
            rousaiKofuNo
            jibaiHokenName
            hokenKbn
            seqNo
            hokenSentaku
          }
          kohi1 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi2 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi3 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
          kohi4 {
            checkDate
            sinDate
            endDate
            isEmptyModel
            kohiName
            houbetu
            futansyaNo
            jyukyusyaNo
            tokusyuNo
            onlineConfirmCheckDate
            hokenId
            isExpirated
            startDate
            seqNo
          }
        }
      }
    }
  }
`;

export const VALIDATE_KOHI_INSURANCE = gql`
  mutation postApiPatientInforValidateKohi(
    $payload: EmrCloudApiRequestsInsuranceValidateKohiRequestInput
  ) {
    postApiPatientInforValidateKohi(
      emrCloudApiRequestsInsuranceValidateKohiRequestInput: $payload
    ) {
      data {
        details {
          displayTextMst
          endDate
          fieldName
          insuranceTitle
          insuranceType
          messageContent
          messageTitle
          startDate
          status
          title
          typeMessage
        }
        resultCheck
      }
      message
      status
    }
  }
`;

export const GET_API_PATIENT_INFOR_CHECK_PATIENT_INFO_DIFFERENCE = gql`
  query getApiPatientInforCheckPatientInfoDifference(
    $onlineConfHisId: BigInt
    $ptId: BigInt
  ) {
    getApiPatientInforCheckPatientInfoDifference(
      onlineConfHisId: $onlineConfHisId
      ptId: $ptId
    ) {
      data {
        checkPatientInfoDifferenceModel {
          address {
            isMap
            value
          }
          birthDay {
            isMap
            value
          }
          homePost {
            isMap
            value
          }
          kanaName {
            isMap
            value
          }
          name {
            isMap
            value
          }
          setainusi {
            isMap
            value
          }
          sex {
            isMap
            value
          }
        }
      }
      message
      status
    }
  }
`;

export const COMPARE_INSURANCE = gql`
  mutation postApiPatientInforCheckPatientHokenInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckPatientHokenInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckPatientHokenInfoDifference(
      emrCloudApiRequestsPatientInforCheckPatientHokenInfoDifferenceRequestInput: $payload
    ) {
      data {
        checkPatientHokenInfoDifference {
          hokenId
          isMapAll
          seqNo
          hokenName
          bango {
            value
            isMap
            xmlValue
          }
          endDate {
            value
            isMap
            xmlValue
          }
          hokenEdaNo {
            value
            isMap
            xmlValue
          }
          hokenInfo {
            value
            isMap
            xmlValue
          }
          hokensyaNo {
            value
            isMap
            xmlValue
          }
          honkeKbn {
            value
            isMap
            xmlValue
          }
          kigo {
            value
            isMap
            xmlValue
          }
          kofuDate {
            value
            isMap
            xmlValue
          }
          kogakuKbn {
            value
            isMap
            xmlValue
          }
          startDate {
            value
            isMap
            xmlValue
          }
        }
      }
    }
  }
`;

export const COMPARE_PUBLIC_EXPENSE = gql`
  mutation postApiPatientInforCheckKohiInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckKohiInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckKohiInfoDifference(
      emrCloudApiRequestsPatientInforCheckKohiInfoDifferenceRequestInput: $payload
    ) {
      data {
        checkKohiInfoDifference {
          hokenId
          isMapAll
          seqNo
          kohiName
          birthDay {
            isMap
            value
            xmlValue
          }
          endDate {
            isMap
            value
            xmlValue
          }
          futansyaNo {
            isMap
            value
            xmlValue
          }
          gendogaku {
            isMap
            value
            xmlValue
          }
          hokenEdaNo {
            value
            isMap
            xmlValue
          }
          jyukyusyaNo {
            isMap
            value
            xmlValue
          }
          startDate {
            isMap
            value
            xmlValue
          }
        }
      }
    }
  }
`;

export const COMPARE_PMH_KOHI = gql`
  mutation postApiPatientInforCheckPmhKohiInfoDifference(
    $payload: EmrCloudApiRequestsPatientInforCheckPmhKohiInfoDifferenceRequestInput
  ) {
    postApiPatientInforCheckPmhKohiInfoDifference(
      emrCloudApiRequestsPatientInforCheckPmhKohiInfoDifferenceRequestInput: $payload
    ) {
      message
      status
      data {
        checkPmhKohiInfoDifferenceDto {
          pmhKohiInfoDifferenceProperties {
            index
            isMap
            kohiValue
            name
            xmlValue
          }
          hokenId
          hokenName
          isContinue
          isNotExistKohiCompare
          isMapAll
          seqNo
        }
      }
    }
  }
`;

export const SAVE_MY_CARD = gql`
  mutation postApiPatientInforSaveOnlineMyCardBeforeReception(
    $payload: EmrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput
  ) {
    postApiPatientInforSaveOnlineMyCardBeforeReception(
      emrCloudApiRequestsPatientInforSaveOnlineMyCardBeforeReceptionRequestInput: $payload
    ) {
      data {
        hokenIds
        kohiIds
        status
      }
    }
  }
`;
