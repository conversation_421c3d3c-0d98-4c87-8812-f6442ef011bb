import { gql } from "@apollo/client";

export const FIND_PHARMACY_RESERVES = gql`
  query findPharmacyReserves($input: FindPharmacyReservesInput!) {
    findPharmacyReserves(input: $input) {
      pharmacyReserveId
      patient {
        ptId
        ptNum
        kanaName
        name
        sex
        birthday
        portalCustomerId
      }
      customer {
        customerId
        kanaName
        name
        gender
        birthday
      }
      desiredDateStatus
      desiredDate {
        pharmacyDesiredDateId
        desiredType
        desiredDate
      }
      reserveUpdateDate
      reserve {
        reserveId
        clinicName
        reserveStartDate
        reserveEndDate
        meetingStatus
      }
      smsStatus
      videocallStatus
      meeting {
        meetingId
        status
      }
      postalServiceType
      csvStatus
      # memo
      pharmacyReserveDetails {
        pharmacyReserveDetailId
        patient {
          ptId
          ptNum
          kanaName
          name
          sex
          birthday
          portalCustomerId
        }
        customer {
          customerId
          kanaName
          name
          gender
          birthday
        }
        prescriptionType
        status
        guidanceStatus
        paymentStatus
        usesElectronicPrescription
        hasElectronicPrescription
        redemptionNumber
      }
    }
  }
`;

export const HAS_PHARMACY_RESERVE_IN_RANGE = gql`
  query hasPharmacyReserveInRange($input: HasPharmacyReserveInRangeInput!) {
    hasPharmacyReserveInRange(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_DETAIL_STATUS = gql`
  mutation updatePharmacyReserveDetailStatus(
    $input: UpdatePharmacyReserveDetailStatusInput!
  ) {
    updatePharmacyReserveDetailStatus(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_DETAIL_GUIDANCE_STATUS = gql`
  mutation updatePharmacyReserveDetailGuidanceStatus(
    $input: UpdatePharmacyReserveDetailGuidanceStatusInput!
  ) {
    updatePharmacyReserveDetailGuidanceStatus(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_POSTAL_SERVICE_TYPE = gql`
  mutation updatePharmacyReservePostalServiceType(
    $input: UpdatePharmacyReservePostalServiceTypeInput!
  ) {
    updatePharmacyReservePostalServiceType(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_DESIRED_DATE = gql`
  mutation updatePharmacyReserveDesiredDate(
    $input: UpdatePharmacyReserveDesiredDateInput!
  ) {
    updatePharmacyReserveDesiredDate(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_DETAIL_STATUS_BY_PHARMACY_RESERVE = gql`
  mutation updatePharmacyReserveDetailStatusByPharmacyReserve(
    $input: UpdatePharmacyReserveDetailStatusByPharmacyReserveInput!
  ) {
    updatePharmacyReserveDetailStatusByPharmacyReserve(input: $input)
  }
`;

export const UPDATE_PHARMACY_RESERVE_CSV_STATUS = gql`
  mutation updatePharmacyReserveCSVStatus(
    $input: UpdatePharmacyReserveCSVStatusInput!
  ) {
    updatePharmacyReserveCSVStatus(input: $input)
  }
`;

export const GET_PHARMACY_RESERVE_CONTACT = gql`
  query getPharmacyReserveContact($input: GetPharmacyReserveContactInput!) {
    getPharmacyReserveContact(input: $input) {
      customer {
        customerId
        kanaName
        name
        gender
        birthday
        telephone
        email
        post_code
        address
      }
      clinic {
        hospital_id
        name
        telephone
        fax
        email
        post_code
        address
      }
      deliveryAddress {
        name
        post_code
        address
        phone_number
      }
    }
  }
`;

export const GET_PHARMACY_RESERVE_INSURANCE = gql`
  query getPharmacyReserveInsurance($input: GetPharmacyReserveInsuranceInput!) {
    getPharmacyReserveInsurance(input: $input) {
      insuranceImageUrl
      medicalCertificateImageUrls
    }
  }
`;

export const GET_PHARMACY_RESERVE_PATIENT_FILES = gql`
  query getPharmacyPatientFiles($input: getPharmacyPatientFilesInput!) {
    getPharmacyPatientFiles(input: $input) {
      pharmacyPatientFileID
      pharmacyReserveDetailID
      patientID
      originalFileName
      s3Key
      createdAt
      URL
      type
      mimeType
    }
  }
`;

export const UPDATE_GUIDANCE_STATUS_BY_PHARMACY_RESERVE = gql`
  mutation updatePharmacyReserveDetailGuidanceStatusByPharmacyReserve(
    $input: UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveInput!
  ) {
    updatePharmacyReserveDetailGuidanceStatusByPharmacyReserve(input: $input)
  }
`;

export const GET_PHARMACY_RESERVE_DETAIL_SURVEY_INFO = gql`
  query getPharmacyReserveDetailSurveyInfo($input: Int!) {
    getPharmacyReserveDetailSurveyInfo(pharmacyReserveDetailId: $input) {
      basicAnswerJson
      pharmacyAnswerJson
      currentMedications
      surveyAnswerDate
      hasMedicationFiles
      medicationFormAnswerDate
      hasPrescriptionRecord
      genericDrugDesire
    }
  }
`;

// export const UPDATE_PHARMACY_RESERVE_MEMO = gql`
//   mutation updatePharmacyReserveMemo($input: UpdatePharmacyReserveMemoInput!) {
//     updatePharmacyReserveMemo(input: $input)
//   }
// `;
