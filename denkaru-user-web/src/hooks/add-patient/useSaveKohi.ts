import { useEffect, useMemo, useState } from "react";

import { useForm } from "react-hook-form";
import { isEmpty, omit } from "lodash";
import dayjs from "dayjs";

import { useGetApiPatientInforGetKohiDetailQuery } from "@/apis/gql/operations/__generated__/insurance";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { numberToDate, toDateNumber } from "@/utils/add-patient";
import {
  BLANK_SELECTED_PUBLIC_EXPENSE,
  CREATE_INSURANCE_DEFAULT_PAYLOAD,
  SAMPLE_CONFIRM_DATE,
} from "@/constants/insurance";
import { usePostApiPatientInforSaveKohiMutation } from "@/apis/gql/operations/__generated__/reception";
import { usePostApiPatientInforValidateKohiMutation } from "@/apis/gql/operations/__generated__/patient-infor";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { dateToNumber } from "@/utils/datetime-format";
import { Emitter } from "@/utils/event-emitter";
import { EMITTER_EVENT } from "@/constants/event";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";
import { sDateToAge } from "@/components/common/Patient/AddPatient/InsuranceCardModal/helper";

import { useUploadFileCard } from "./useUploadFileCard";
import { usePublicExpenseSelectDefaultValue } from "./usePublicExpenseSelectDefaultValue";
import { useUpdateRefNo } from "./useUpdateRefNo";
import { useGetPatientInfoById } from "./useGetPatientInfoById";

import type { EndDateModels } from "@/types/patient";
import type {
  InsuranceCardFormType,
  RegistrationPublicExpenseInforForm,
  ValidateKohiContent,
} from "@/types/insurance";
import type { EmrCloudApiResponsesInsuranceKohiInfDtoInput } from "@/apis/gql/generated/types";
import type { CustomUploadFile } from "@/types/file";

const defaultValues: RegistrationPublicExpenseInforForm = {
  futansyaNo: "",
  jyukyusyaNo: "",
  insuranceType: "please_select",
  rate: 0,
  gendogaku: 0,
  tokusyuNo: "",
  limitList: [],
  confirmDateList: [],
};

type Props = {
  modalKey?: number;
};

export const useSaveKohi = ({ modalKey }: Props) => {
  const [errorContent, setErrorContent] = useState<ValidateKohiContent>();
  const [fileCard, setFileCard] = useState<CustomUploadFile[]>([]);

  const {
    selectedPatient: patientInfo,
    selectedInsurance,
    selectedSinDate,
    handleCloseModal,
    callbackList,
    confirmingType,
    onlineMasterData,
    handleUpdateOnlineMasterData,
    handleSetCallback,
    setSelectedInsurance,
    modal,
    handleOpenModal,
    duplicateModal,
    changeConfirmingType,
    handleCloseDuplicatePublicExpenseModal,
    onlineInsuranceData,
    resetOnlineData,
    onlineConfirmHistoryData,
  } = usePatientContext();

  const ptId = patientInfo?.patientID.toString();

  const { optionsPublicExpense, publicExpenseSelectData } =
    usePublicExpenseSelectDefaultValue({
      ptId,
      sinDate: selectedSinDate,
    });

  const { handleError } = useErrorHandler();

  const methods = useForm<RegistrationPublicExpenseInforForm>({
    mode: "onSubmit",
    defaultValues,
  });
  const { watch, reset, setError, getValues, setValue, setFocus } = methods;

  const { file, insuranceType } = watch();

  const selectedPublicExpense =
    publicExpenseSelectData.find((item) => item.uniqKey === insuranceType) ||
    BLANK_SELECTED_PUBLIC_EXPENSE;

  const { uploadInsuranceCard, handleFileSend } = useUploadFileCard({
    ptId: patientInfo?.patientID ?? 0,
    fileNo: 2,
  });
  const { patientDetail } = useGetPatientInfoById({ ptId }, !ptId);

  const { handleUpdateRefNo } = useUpdateRefNo({
    screenCode: SystemScreenCode.Reception,
    systemHub:
      confirmingType === "ADDING_PATIENT_MY_INSURANCE"
        ? SystemHub.PatientInf
        : SystemHub.Reception,
  });

  const handleClose = () => {
    handleCloseModal("CHANGE_EXPIRATION_DATE");
    if (modalKey) {
      handleCloseDuplicatePublicExpenseModal(modalKey);
    } else {
      handleCloseModal("HANDLE_PUBLIC_EXPENSE");
      setSelectedInsurance(null);
    }
    if (modal.compareIdentityOpen) {
      handleCloseModal("COMPARE_IDENTITY");
    }
    if (modal.comparePmhOpen) {
      handleCloseModal("COMPARE_PMH");
    }
    if (
      confirmingType &&
      [
        "EDITING_KOHI_MY_INSURANCE",
        "CONFIRMING_KOHI_MY_INSURANCE",
        "ADDING_KOHI_MY_INSURANCE",
      ].includes(confirmingType)
    ) {
      changeConfirmingType(null);
      resetOnlineData();
    }
    if (!confirmingType && !callbackList.length) return;
    const newCallbackList = [...callbackList];
    newCallbackList.shift();
    handleSetCallback([...newCallbackList]);
  };

  const [handleSaveKohi, { loading: loadingSaveKohi }] =
    usePostApiPatientInforSaveKohiMutation({
      onError: (error) => {
        handleError({ error });
      },
      onCompleted: (res) => {
        const kohiId = res.postApiPatientInforSaveKohi?.data?.kohiId;
        if (!file) {
          handleFileSend({
            hokenId: kohiId ?? 0,
            isDelete: true,
          });
        }
        if (kohiId && fileCard.length && file) {
          uploadInsuranceCard({ fileAttach: fileCard, hokenId: kohiId });
        }
        if (
          ptId &&
          confirmingType &&
          [
            "EDITING_KOHI_MY_INSURANCE",
            "CONFIRMING_KOHI_MY_INSURANCE",
            "ADDING_KOHI_MY_INSURANCE",
            "ADDING_PATIENT_MY_INSURANCE",
            "ADDING_PATIENT_MY_CARD",
          ].includes(confirmingType) &&
          [0, 3].includes(callbackList.length)
        ) {
          handleUpdateRefNo({
            ptId,
            resultOfQualificationConfirmation:
              onlineConfirmHistoryData?.qcXmlMsgResponse?.messageBody
                ?.resultList?.resultOfQualificationConfirmation?.[0],
            ptNum: String(patientDetail?.ptNum),
            isBiggerThan75YearsOld:
              sDateToAge(
                Number(dayjs(patientInfo?.birthdate).format("YYYYMMDD")) || 0,
                selectedSinDate,
              ) > 75,
            isKohi:
              confirmingType === "ADDING_PATIENT_MY_CARD"
                ? onlineConfirmHistoryData?.qcXmlMsgResponse?.messageBody
                    ?.resultList?.resultOfQualificationConfirmation?.[0]
                    ?.insuredCardClassification === "A1"
                : true,
          });
        }
        handleClose();
      },
      refetchQueries: [
        "getApiPatientInforGetKohiInfByPtId",
        "getApiPatientInforGetHokenPatternByPtId",
        "getApiPatientInforGetPatientById",
      ],
    });

  const [handleValidateSaveKohi, { loading: loadingValidateSaveKohi }] =
    usePostApiPatientInforValidateKohiMutation();

  const { data } = useGetApiPatientInforGetKohiDetailQuery({
    skip: !ptId || !selectedInsurance?.hokenId || !selectedSinDate,
    variables: {
      hokenId: selectedInsurance?.hokenId ?? undefined,
      ptId,
      sinDate: selectedSinDate,
      seqNo: Number(selectedInsurance?.seqNo) || undefined,
    },
  });

  const getKohiPayload = (
    data: RegistrationPublicExpenseInforForm,
  ): EmrCloudApiResponsesInsuranceKohiInfDtoInput => {
    const hokenMstModel = omit(selectedPublicExpense, [
      "__typename",
      "uniqKey",
      "isOtherPrefValid",
    ]);

    const limitListModel = data.limitList
      .filter((item) => item.sinDateD)
      .map((item) => {
        return {
          id: item.id ?? "0",
          kohiId: item.kohiId,
          sinDate: item.sinDate,
          hokenPid: item.hokenPid,
          sortKey: item.sortKey,
          raiinNo: item.raiinNo,
          futanGaku: item.futanGaku,
          totalGaku: item.totalGaku,
          biko: item.biko,
          isDeleted: item.isDeleted,
          sort: item.sort,
          totalMoney: item.totalMoney,
          seqNo: item.seqNo,
        };
      });

    const confirmDateUpdate = data.confirmDateList?.find(
      (item) => item.confirmDate === toDateNumber(data.checkDate),
    );

    const newConfirmDates = {
      ...(confirmDateUpdate || SAMPLE_CONFIRM_DATE),
      confirmDate: toDateNumber(data.checkDate),
      hokenGrp: confirmingType ? 2 : 0,
      onlineConfirmationId:
        onlineMasterData?.onlineConfirmHistoryId ||
        confirmDateUpdate?.onlineConfirmationId ||
        0,
    };

    return {
      confirmDateList: [
        omit(newConfirmDates, ["isDeleted", "checkMachine", "ptId"]),
      ],
      hokenMstModel,
      futansyaNo: data.futansyaNo,
      jyukyusyaNo: data.jyukyusyaNo ? String(data.jyukyusyaNo) : "",
      startDate: toDateNumber(data.startDate),
      endDate: data.endDate ? toDateNumber(data.endDate) : 99999999,
      confirmDate: toDateNumber(data.checkDate),
      rate: data.rate ? Number(data.rate) : 0,
      gendoGaku: data.gendogaku ? Number(data.gendogaku) : 0,
      sikakuDate: toDateNumber(data.sikakuDate),
      kofuDate: toDateNumber(data.kofuDate),
      tokusyuNo: data.tokusyuNo ? String(data.tokusyuNo) : "",
      houbetu: selectedPublicExpense.houbetu,
      sinDate: selectedSinDate,
      isHaveKohiMst:
        !!data.insuranceType || data.insuranceType !== "please_select",
      hokenEdaNo: selectedInsurance?.isPmh
        ? selectedPublicExpense.hokenEdaNo || 0
        : (data.hokenEdaNo ?? (selectedPublicExpense.hokenEdaNo || 0)),
      hokenNo: selectedInsurance?.isPmh
        ? selectedPublicExpense.hokenNo || 0
        : (data.hokenNo ?? (selectedPublicExpense.hokenNo || 0)),
      prefNoMst: data.prefNoMst ?? "prefNoMst",
      prefNo: selectedPublicExpense.prefNo || 0,
      isAddNew: !selectedInsurance,
      seqNo: String(selectedInsurance?.seqNo ?? 0),
      hokenId: selectedInsurance?.hokenId ?? 0,
      birthday: toDateNumber(data.birthday),
      hokenSbtKbn: hokenMstModel.hokenSbtKbn,
      limitListModel,
    };
  };

  const saveKohi = ({
    data,
    kohiPayload,
    endDateModels,
  }: {
    data: RegistrationPublicExpenseInforForm;
    kohiPayload: EmrCloudApiResponsesInsuranceKohiInfDtoInput;
    endDateModels?: EndDateModels[];
  }) => {
    const limitList = data.limitList
      .map((limit) => omit(limit, ["isModify", "key", "hasError"]))
      .filter((item) => item.sinDateD);

    if (
      !confirmingType ||
      !["ADDING_RECEPTION_MY_CARD", "VIEW_RESULT_MY_CARD"].includes(
        confirmingType,
      )
    ) {
      const endDateOfCurrentInsurance =
        onlineInsuranceData?.endDateModel?.endDate;
      if (
        !endDateModels?.[0] &&
        endDateOfCurrentInsurance &&
        numberToDate(endDateOfCurrentInsurance).isAfter(
          numberToDate(selectedSinDate),
        )
      ) {
        handleOpenModal("CHANGE_EXPIRATION_DATE");
        return;
      }

      if (endDateModels?.[0]?.endDate) {
        Emitter.emit(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, {
          hokenId: selectedInsurance?.hokenId,
          endDate: numberToDate(endDateModels[0].endDate),
        });
      }

      const endDateModel = omit(endDateModels?.[0], ["name", "startDate"]);

      handleSaveKohi({
        variables: {
          ptId,
          kohi: kohiPayload,
          limitList,
          ...(confirmingType === "ADDING_PATIENT_MY_INSURANCE"
            ? CREATE_INSURANCE_DEFAULT_PAYLOAD
            : (onlineInsuranceData ?? CREATE_INSURANCE_DEFAULT_PAYLOAD)),
          endDateModel: !isEmpty(endDateModel) ? endDateModel : undefined,
          patientInfo: onlineInsuranceData?.patientInfo ?? [],
        },
      });
      return;
    }

    const endDateOfCurrentInsurance = onlineMasterData?.endDateModels?.find(
      (item) => item.pending,
    );

    if (
      !endDateModels?.some(
        (item) => item.hokenId === endDateOfCurrentInsurance?.hokenId,
      ) &&
      endDateOfCurrentInsurance?.endDate &&
      numberToDate(endDateOfCurrentInsurance?.endDate).isAfter(
        numberToDate(selectedSinDate),
      )
    ) {
      handleOpenModal("CHANGE_EXPIRATION_DATE");
      return;
    }

    handleUpdateOnlineMasterData({
      listKohiModel: [
        ...(onlineMasterData?.listKohiModel ?? []),
        {
          ...kohiPayload,
        },
      ],
      endDateModels,
    });
    if (modal.compareIdentityOpen) {
      handleCloseModal("COMPARE_IDENTITY");
    }
    if (modal.compareMaruchoOpen) {
      handleCloseModal("COMPARE_MARUCHO");
    }
    handleClose();
  };

  const onSubmitForm = (data: RegistrationPublicExpenseInforForm) => {
    handleValidateSaveKohi({
      variables: {
        payload: {
          kohi: getKohiPayload(data),
          ptId,
          ptBirthday: patientInfo?.birthdate
            ? dateToNumber(new Date(patientInfo?.birthdate))
            : 0,
          sindate: selectedSinDate,
        },
      },
      onError: (error) => {
        handleError({ error });
      },
      onCompleted: (res) => {
        const dataRes = res.postApiPatientInforValidateKohi?.data?.details;

        if (!dataRes?.length) {
          saveKohi({ data, kohiPayload: getKohiPayload(data) });
          return;
        }

        setFocus(
          dataRes[0]?.fieldName as keyof RegistrationPublicExpenseInforForm,
        );

        dataRes.forEach((item) => {
          if (item.typeMessage === 8) {
            setError(
              item.fieldName as keyof RegistrationPublicExpenseInforForm,
              {
                message: item.messageTitle,
              },
            );
            return;
          }
          if (item.typeMessage === 4) {
            setErrorContent({
              insuranceTitle: item.insuranceTitle ?? "",
              messageTitle: item.messageTitle ?? "",
              messageContent: item.messageContent ?? "",
              title: item.title ?? "",
            });
          }
          return;
        });
      },
    });
  };

  const handleConfirmSaveKohi = () => {
    const dataForm = getValues();
    const kohiPayload = getKohiPayload(dataForm);
    saveKohi({ data: dataForm, kohiPayload });
  };

  const handleUpdatedExpirationDate = (endDateModels: EndDateModels[]) => {
    const data = {
      ...getValues(),
    };
    const kohiPayload = getKohiPayload(data);

    saveKohi({ data, kohiPayload, endDateModels });
  };

  const duplicatedInsuranceValue = useMemo(() => {
    if (!modalKey) return;
    if (!duplicateModal.publicExpense[modalKey]) return;

    return {
      ...duplicateModal.publicExpense[modalKey],
    };
  }, [duplicateModal.publicExpense, modalKey]);

  useEffect(() => {
    if (duplicatedInsuranceValue) {
      return reset({
        ...defaultValues,
        ...duplicatedInsuranceValue,
      });
    }

    const kohiDetail = data?.getApiPatientInforGetKohiDetail?.data?.data;
    if (!kohiDetail) {
      if (!selectedInsurance) return;
      reset({
        ...defaultValues,
        ...selectedInsurance,
      });
      return;
    }

    reset({
      ...defaultValues,
      endDate:
        kohiDetail.endDate && kohiDetail.endDate !== 99999999
          ? numberToDate(kohiDetail.endDate)
          : 0,
      ...(selectedInsurance?.isPmh ? selectedInsurance : {}),
      startDate: kohiDetail.startDate ? numberToDate(kohiDetail.startDate) : 0,
      birthday: kohiDetail.birthday ? numberToDate(kohiDetail.birthday) : 0,
      checkDate: selectedSinDate ? numberToDate(selectedSinDate) : 0,
      futansyaNo: kohiDetail.futansyaNo,
      gendogaku: kohiDetail.gendoGaku,
      jyukyusyaNo: kohiDetail.jyukyusyaNo,
      rate: kohiDetail.rate,
      tokusyuNo: kohiDetail.tokusyuNo,
      sikakuDate: kohiDetail.sikakuDate
        ? numberToDate(kohiDetail.sikakuDate)
        : 0,
      kofuDate: kohiDetail.kofuDate ? numberToDate(kohiDetail.kofuDate) : 0,
      limitList: [],
      confirmDateList: kohiDetail.confirmDateList,
      insuranceType: `${kohiDetail.hokenEdaNo}__${kohiDetail.hokenNo}`,
      file: kohiDetail.filingInfModels?.[0],
      ...(!selectedInsurance?.isPmh ? selectedInsurance : {}),
    });
  }, [
    data?.getApiPatientInforGetKohiDetail?.data?.data,
    duplicatedInsuranceValue,
    reset,
    selectedInsurance,
    selectedSinDate,
  ]);

  useEffect(() => {
    const handleUpdateValues = ({
      hokenId,
      ...rest
    }: InsuranceCardFormType) => {
      if (selectedInsurance?.hokenId === hokenId) {
        setValue("endDate", rest.endDate);
      }
    };
    Emitter.once(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, (rest) =>
      handleUpdateValues(rest),
    );

    return () => {
      Emitter.off(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, (rest) =>
        handleUpdateValues(rest),
      );
    };
  }, [selectedInsurance?.hokenId, setValue]);

  const handleDeleteFile = () => {
    setValue("file", undefined);
  };

  return {
    methods,
    onSubmitForm,
    loadingSaveKohi: loadingValidateSaveKohi || loadingSaveKohi,
    errorContent,
    setErrorContent,
    handleConfirmSaveKohi,
    handleUpdateExpirationDate: handleUpdatedExpirationDate,
    setFileCard,
    fileCard,
    handleDeleteFile,
    optionsPublicExpense,
    publicExpenseSelectData,
    selectedPublicExpense,
  };
};
