import { useCallback, useEffect, useMemo, useState } from "react";

import { useForm } from "react-hook-form";
import { filter, isEmpty, omit, sortBy } from "lodash";
import dayjs from "dayjs";

import {
  useGetApiInsuranceMstGetHokenInfQuery,
  usePostApiPatientInforSaveInsuranceInfoMutation,
} from "@/apis/gql/operations/__generated__/insurance";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { numberToDate, toDateNumber } from "@/utils/add-patient";
import {
  CREATE_INSURANCE_DEFAULT_PAYLOAD,
  DEFAULT_HOKEN_KOGAKU_KBN,
  DEFAULT_VALUES_INSURANCE_CARD,
  HOKEN_CONSTANTS,
  HOKEN_MST_MODEL,
  HOKENSYA_MST_MODEL,
  SAMPLE_CONFIRM_DATE,
  SAMPLE_INSURANCE_INFO,
} from "@/constants/insurance";
import {
  convertDateToNumber,
  convertYYYYMMtoNumber,
  formatToYYYYMM,
  getHokenKbnFromHoubetu,
  getHokenMstFromHokensyaNo,
  processHokenKogakuKbn,
  sDateToAge,
} from "@/components/common/Patient/AddPatient/InsuranceCardModal/helper";
import { usePostApiPatientInforValidateHokenMutation } from "@/apis/gql/operations/__generated__/patient-infor";
import { MAX_DATE } from "@/constants/setting-master";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { dateToNumber } from "@/utils/datetime-format";
import { Emitter } from "@/utils/event-emitter";
import { EMITTER_EVENT } from "@/constants/event";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";

import { useDebounce } from "../useDebounce";
import { useGlobalNotification } from "../useGlobalNotification";

import { useGetInsuranceCardComboboxQuery } from "./useGetInsuranceCardComboboxQuery";
import { useUploadFileCard } from "./useUploadFileCard";
import { useUpdateRefNo } from "./useUpdateRefNo";
import { useGetPatientInfoById } from "./useGetPatientInfoById";

import type { CustomUploadFile } from "@/types/file";
import type { EndDateModels } from "@/types/patient";
import type { InsuranceCardFormType, Option } from "@/types/insurance";
import type {
  DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenInfValidHokenInfStatus,
  EmrCloudApiRequestsPatientInforBasicPatientInfoSaveInsuranceInfoRequestInput,
} from "@/apis/gql/generated/types";

type Payload =
  EmrCloudApiRequestsPatientInforBasicPatientInfoSaveInsuranceInfoRequestInput;

type Props = {
  modalKey?: number;
};

export const useInsuranceCard = ({ modalKey }: Props) => {
  const {
    setSelectedInsurance,
    selectedPatient: patientInfo,
    selectedInsurance,
    selectedSinDate: sinDate,
    handleCloseModal,
    callbackList,
    handleSetCallback,
    onlineMasterData,
    confirmingType,
    handleUpdateOnlineMasterData,
    handleOpenModal,
    modal,
    onlineInsuranceData,
    handleCloseDuplicateInsuranceCardModal,
    duplicateModal,
    changeConfirmingType,
    resetOnlineData,
    onlineConfirmHistoryData,
  } = usePatientContext();
  const { handleUpdateRefNo } = useUpdateRefNo({
    screenCode: SystemScreenCode.Reception,
    systemHub:
      confirmingType === "ADDING_PATIENT_MY_INSURANCE"
        ? SystemHub.PatientInf
        : SystemHub.Reception,
  });

  const ptId = String(patientInfo?.patientID ?? 0);
  const [insuranceValidation, setInsuranceValidation] = useState<
    DomainModelsInsuranceResultValidateHokenPattern1UseCaseInsuranceValidateHokenInfValidHokenInfStatus[]
  >([]);
  const [fileCard, setFileCard] = useState<CustomUploadFile[]>([]);

  const { patientDetail } = useGetPatientInfoById({ ptId }, !ptId);
  const { comboBoxData, hokenMstOptions, tokkiOptions } =
    useGetInsuranceCardComboboxQuery({
      ptId,
      sinDate,
    });
  const { handleError } = useErrorHandler();
  const { notification } = useGlobalNotification();

  const handleClose = useCallback(() => {
    handleCloseModal("CHANGE_EXPIRATION_DATE");

    if (modalKey) {
      handleCloseDuplicateInsuranceCardModal(modalKey);
    } else {
      handleCloseModal("HANDLE_INSURANCE_CARD");
      setSelectedInsurance(null);
    }
    if (modal.compareHokenOpen) {
      handleCloseModal("COMPARE_HOKEN");
    }
    if (
      confirmingType &&
      [
        "EDITING_HOKEN_MY_INSURANCE",
        "CONFIRMING_HOKEN_MY_INSURANCE",
        "ADDING_HOKEN_MY_INSURANCE",
      ].includes(confirmingType)
    ) {
      changeConfirmingType(null);
      resetOnlineData();
    }

    if (!confirmingType && !callbackList.length) return;
    const newCallbackList = [...callbackList];
    newCallbackList.shift();
    handleSetCallback([...newCallbackList]);
  }, [
    callbackList,
    changeConfirmingType,
    confirmingType,
    handleCloseDuplicateInsuranceCardModal,
    handleCloseModal,
    handleSetCallback,
    modal.compareHokenOpen,
    modalKey,
    resetOnlineData,
    setSelectedInsurance,
  ]);

  const { uploadInsuranceCard, handleFileSend } = useUploadFileCard({
    ptId: Number(ptId),
    fileNo: 1,
  });

  const [saveInsuranceInfo, { loading: loadingSaveInsurance }] =
    usePostApiPatientInforSaveInsuranceInfoMutation({
      onError: (error) => handleError({ error }),
      onCompleted: (data) => {
        const hokenId =
          data.postApiPatientInforSaveInsuranceInfo?.data?.hokenId;
        if (!file) {
          handleFileSend({
            hokenId: hokenId ?? 0,
            isDelete: true,
          });
        }
        if (hokenId) {
          if (fileCard.length && file) {
            uploadInsuranceCard({ fileAttach: fileCard, hokenId });
          }
        }
        if (
          confirmingType &&
          [
            "ADDING_PATIENT_MY_INSURANCE",
            "CONFIRMING_HOKEN_MY_INSURANCE",
            "ADDING_HOKEN_MY_INSURANCE",
            "EDITING_HOKEN_MY_INSURANCE",
            "ADDING_PATIENT_MY_CARD",
          ].includes(confirmingType) &&
          [0, 3].includes(callbackList.length)
        ) {
          handleUpdateRefNo({
            ptId,
            resultOfQualificationConfirmation:
              onlineConfirmHistoryData?.qcXmlMsgResponse?.messageBody
                ?.resultList?.resultOfQualificationConfirmation?.[0],
            ptNum: String(patientDetail?.ptNum),
            isBiggerThan75YearsOld:
              sDateToAge(
                Number(dayjs(patientInfo?.birthdate).format("YYYYMMDD")) || 0,
                sinDate,
              ) > 75,
          });
        }
        handleClose();
      },
      refetchQueries: [
        "insuranceGetApiPatientInforGetHokenInfByPtId",
        "getApiPatientInforGetHokenPatternByPtId",
        "getApiPatientInforGetPatientById",
      ],
    });

  const methods = useForm<InsuranceCardFormType>({
    defaultValues: { ...DEFAULT_VALUES_INSURANCE_CARD, ptId },
  });

  const {
    watch,
    reset,
    setError,
    formState: { errors },
    getValues,
    setValue,
  } = methods;

  const { startDate, endDate, hokensyaNo, hokenMstKey, file } = watch();

  const [validateInsurance, { loading: loadingValidateInsurance }] =
    usePostApiPatientInforValidateHokenMutation({
      onError: (error) => handleError({ error }),
      onCompleted: (data) => {
        const validationResponse =
          data.postApiPatientInforValidateHoken?.data?.detail;
        if (!validationResponse?.length) return;
        const inlineContents = [...validationResponse].filter(
          (item) => item.typeMessage === 8,
        );

        inlineContents.forEach((item) => {
          if (
            item.fieldName &&
            item.fieldName in DEFAULT_VALUES_INSURANCE_CARD
          ) {
            setError(item.fieldName as keyof InsuranceCardFormType, {
              message: item.messageTitle,
            });
          }
        });

        const errorInsuranceDeleted = validationResponse.find(
          (item) => item.status === 14,
        );
        if (errorInsuranceDeleted) {
          notification.error({ message: errorInsuranceDeleted.messageTitle });
        }

        const popupContent = sortBy(
          [...validationResponse].filter(
            (item) => item.typeMessage !== 8 && item.status !== 14,
          ),
          ["typeMessage"],
        );
        if (popupContent.length) {
          setInsuranceValidation(popupContent);
        }
      },
    });

  const hokenInfoQuery = useGetApiInsuranceMstGetHokenInfQuery({
    skip: !ptId || !selectedInsurance,
    variables: {
      hokenId: selectedInsurance?.hokenId ?? undefined,
      ptId,
    },
  });

  const kogakuKbnOptions = useMemo((): Array<Option<string>> => {
    const options = processHokenKogakuKbn(
      {
        startDate: startDate ? convertDateToNumber(startDate) : 0,
        endDate: endDate ? convertDateToNumber(endDate) : 99999999,
        hokensyaNo: String(hokensyaNo),
      },
      sinDate,
      patientInfo?.birthdate
        ? dateToNumber(new Date(patientInfo.birthdate))
        : 0,
    );

    if (options.length) {
      return options;
    }
    const kogakuKbn =
      selectedInsurance?.kogakuKbn ||
      hokenInfoQuery.data?.getApiInsuranceMstGetHokenInf?.data?.hokenInfList
        ?.kogakuKbn;
    return [
      {
        label: DEFAULT_HOKEN_KOGAKU_KBN[String(kogakuKbn || 0)] || "",
        value: String(kogakuKbn || 0),
      },
    ];
  }, [
    endDate,
    hokenInfoQuery.data?.getApiInsuranceMstGetHokenInf?.data?.hokenInfList
      ?.kogakuKbn,
    hokensyaNo,
    patientInfo?.birthdate,
    selectedInsurance?.kogakuKbn,
    sinDate,
    startDate,
  ]);

  const hokenCheck = useMemo(() => {
    const currentHokenMst = hokenMstOptions.find(
      (item) => item.value === hokenMstKey,
    );
    return {
      isJihi108: currentHokenMst?.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_108,
      isJihi109: currentHokenMst?.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_109,
      isNashi: currentHokenMst?.houbetu === HOKEN_CONSTANTS.HOUBETU_NASHI,
      isHoken68:
        !!currentHokenMst?.hokenSbtKbn &&
        [0, 8].includes(currentHokenMst.hokenSbtKbn),
    };
  }, [hokenMstKey, hokenMstOptions]);

  const deboucedHokenSyaNo = useDebounce(hokensyaNo, 400);
  const hokenMstOptionsFiltered = useMemo(() => {
    const options = getHokenMstFromHokensyaNo(
      hokenMstOptions,
      deboucedHokenSyaNo,
    );
    const hokenNo68 = hokenMstOptions.find((item) => item.hokenNo === 68);

    if (deboucedHokenSyaNo.length === 8 && !options.length && hokenNo68) {
      return filter(hokenMstOptions, (item) =>
        [8, 9].includes(item.hokenSbtKbn ?? 0),
      );
    }

    if (options.some((item) => item.hokenNo === 68) || !hokenNo68) {
      return options;
    }

    return sortBy([...options, hokenNo68], ["hokenNo"]);
  }, [hokenMstOptions, deboucedHokenSyaNo]);

  const getPayload = useCallback(
    ({
      checkDate,
      startDate,
      endDate,
      hokenMstKey,
      genmenKbn,
      genmenRate,
      honkeKbn,
      keizokuKbn,
      kofuDate,
      kogakuKbn,
      sikakuDate,
      syokumuKbn,
      tasukaiYm,
      tokureiYm1,
      tokureiYm2,
      genmenGaku,
      hokensyaMstModel,
      hokensyaName,
      insuredName,
      confirmDates,
      file: _,
      ...data
    }: InsuranceCardFormType): Payload => {
      const hokenMstFound = comboBoxData.hokenMstAllData.find(
        (item) => item.key === hokenMstKey,
      );

      const confirmDateUpdate = confirmDates?.find(
        (item) => item.confirmDate === toDateNumber(checkDate),
      );

      const newConfirmDates = {
        ...(confirmDateUpdate || SAMPLE_CONFIRM_DATE),
        confirmDate: toDateNumber(checkDate),
        hokenGrp: confirmingType ? 1 : 0,
        onlineConfirmationId:
          onlineMasterData?.onlineConfirmHistoryId ||
          confirmDateUpdate?.onlineConfirmationId ||
          0,
      };

      return {
        hokenInf: {
          ...SAMPLE_INSURANCE_INFO,
          ...omit(data, ["limitConsFlg"]),
          confirmDates: [newConfirmDates],
          startDate: toDateNumber(startDate),
          endDate: toDateNumber(endDate),
          hokenEdaNo: hokenMstFound?.hokenEdaNo ?? 0,
          hokenNo: hokenMstFound?.hokenNo ?? 0,
          houbetu: hokenMstFound?.houbetu ?? "0",
          genmenKbn: Number(genmenKbn),
          genmenRate: Number(genmenRate) || 0,
          genmenGaku: Number(genmenGaku) || 0,
          honkeKbn,
          keizokuKbn,
          kogakuKbn: Number(kogakuKbn),
          syokumuKbn,
          tasukaiYm: convertYYYYMMtoNumber(tasukaiYm),
          tokureiYm1: convertYYYYMMtoNumber(tokureiYm1),
          tokureiYm2: convertYYYYMMtoNumber(tokureiYm2),
          kofuDate: toDateNumber(kofuDate),
          sikakuDate: toDateNumber(sikakuDate),
          isAddNew: !selectedInsurance,
          hokenKbn: getHokenKbnFromHoubetu(
            hokenMstFound?.houbetu ?? "",
            hokenMstFound?.hokenSbtKbn,
          ),
          hokensyaName: hokensyaName?.trim(),
          insuredName: insuredName?.trim(),
          ptId,
        },
        ptId: Number(ptId),
        sinDate,
        hokenMstModel: {
          ...HOKEN_MST_MODEL,
          ...omit(hokenMstFound, ["key"]),
        },
        hokensyaMstModel: {
          ...HOKENSYA_MST_MODEL,
          ...hokensyaMstModel,
        },
      };
    },
    [
      comboBoxData.hokenMstAllData,
      confirmingType,
      onlineMasterData?.onlineConfirmHistoryId,
      ptId,
      selectedInsurance,
      sinDate,
    ],
  );

  const handleSaveInsurance = useCallback(
    (data: InsuranceCardFormType, endDateModels?: EndDateModels[]) => {
      const payload = getPayload(data);

      if (
        !confirmingType ||
        !["ADDING_RECEPTION_MY_CARD", "VIEW_RESULT_MY_CARD"].includes(
          confirmingType,
        )
      ) {
        const endDateOfCurrentInsurance =
          onlineInsuranceData?.endDateModel?.endDate;
        if (
          !endDateModels?.[0] &&
          endDateOfCurrentInsurance &&
          numberToDate(endDateOfCurrentInsurance).isAfter(numberToDate(sinDate))
        ) {
          handleOpenModal("CHANGE_EXPIRATION_DATE");
          return;
        }

        if (endDateModels?.[0]?.endDate) {
          Emitter.emit(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, {
            hokenId: selectedInsurance?.hokenId,
            endDate: numberToDate(endDateModels[0].endDate),
          });
        }

        const endDateModel = omit(endDateModels?.[0], ["name", "startDate"]);

        saveInsuranceInfo({
          variables: {
            input: {
              ...payload,
              ...(confirmingType === "ADDING_PATIENT_MY_INSURANCE"
                ? CREATE_INSURANCE_DEFAULT_PAYLOAD
                : (onlineInsuranceData ?? CREATE_INSURANCE_DEFAULT_PAYLOAD)),
              endDateModel: !isEmpty(endDateModel) ? endDateModel : undefined,
            },
          },
        });
        return;
      }

      const endDateOfCurrentInsurance = onlineMasterData?.endDateModels?.find(
        (item) => item.pending,
      );

      if (
        !endDateModels?.some(
          (item) => item.seqNo === endDateOfCurrentInsurance?.seqNo,
        ) &&
        endDateOfCurrentInsurance?.endDate &&
        numberToDate(endDateOfCurrentInsurance?.endDate).isAfter(
          numberToDate(sinDate),
        )
      ) {
        handleOpenModal("CHANGE_EXPIRATION_DATE");
        return;
      }

      handleUpdateOnlineMasterData({
        listHokenInfModel: [
          ...(onlineMasterData?.listHokenInfModel ?? []),
          {
            ...payload.hokenInf,
            rousaiTenkis: [],
          },
        ],
        endDateModels,
      });
      handleClose();
      handleCloseModal("COMPARE_HOKEN");
    },
    [
      confirmingType,
      getPayload,
      handleClose,
      handleCloseModal,
      handleOpenModal,
      handleUpdateOnlineMasterData,
      onlineInsuranceData,
      onlineMasterData?.endDateModels,
      onlineMasterData?.listHokenInfModel,
      saveInsuranceInfo,
      selectedInsurance?.hokenId,
      sinDate,
    ],
  );

  const handleConfirmValidation = useCallback(() => {
    const newArr = [...insuranceValidation];
    newArr.shift();
    if (!newArr.length && isEmpty(errors)) {
      const data = getValues();
      handleSaveInsurance(data);
    }
    setInsuranceValidation(newArr);
  }, [errors, getValues, handleSaveInsurance, insuranceValidation]);

  const onSubmit = useCallback(
    async (data: InsuranceCardFormType) => {
      const payload = getPayload(data);
      const res = await validateInsurance({
        variables: {
          payload,
        },
      });

      if (!res.data?.postApiPatientInforValidateHoken?.data?.resultCheck) {
        return;
      }

      handleSaveInsurance(data);
    },
    [getPayload, handleSaveInsurance, validateInsurance],
  );

  const handleUpdatedExpirationDate = useCallback(
    (endDateModels: EndDateModels[]) => {
      const data = getValues();
      handleSaveInsurance({ ...data }, endDateModels);
    },
    [getValues, handleSaveInsurance],
  );

  const duplicatedInsuranceValue = useMemo(() => {
    if (!modalKey) return;
    if (!duplicateModal.insuranceCard[modalKey]) return;

    return {
      ...duplicateModal.insuranceCard[modalKey],
    };
  }, [duplicateModal.insuranceCard, modalKey]);

  useEffect(() => {
    if (duplicatedInsuranceValue) {
      return reset({
        ...omit(DEFAULT_VALUES_INSURANCE_CARD, "hokensyaMstModel"),
        ...duplicatedInsuranceValue,
        hokenMstKey: comboBoxData.hokenMstAllData.find((item) => {
          return (
            item.hokenEdaNo === duplicatedInsuranceValue.hokenEdaNo &&
            item.hokenNo === duplicatedInsuranceValue?.hokenNo
          );
        })?.key,
      });
    }

    const insuranceInfo =
      hokenInfoQuery.data?.getApiInsuranceMstGetHokenInf?.data?.hokenInfList;
    if (!insuranceInfo) {
      if (!selectedInsurance) return;
      reset({
        ...omit(DEFAULT_VALUES_INSURANCE_CARD, "hokensyaMstModel"),
        ...selectedInsurance,
        hokenMstKey: comboBoxData.hokenMstAllData.find((item) => {
          return (
            item.hokenEdaNo === selectedInsurance.hokenEdaNo &&
            item.hokenNo === selectedInsurance.hokenNo
          );
        })?.key,
      });
      return;
    }

    const {
      ptId,
      hokenId,
      seqNo,
      hokensyaNo,
      kigo,
      bango,
      edaNo,
      honkeKbn,
      hokensyaName,
      insuredName,
      sikakuDate,
      kofuDate,
      startDate,
      endDate,
      kogakuKbn,
      tasukaiYm,
      tokureiYm1,
      tokureiYm2,
      genmenKbn,
      genmenRate,
      genmenGaku,
      syokumuKbn,
      keizokuKbn,
      tokki1,
      tokki2,
      tokki3,
      tokki4,
      tokki5,
      hokenEdaNo,
      hokenNo,
      confirmDate,
      confirmDateList,
      filingInfModels,
    } = insuranceInfo;

    reset({
      ptId,
      hokensyaNo,
      kigo,
      bango,
      edaNo,
      honkeKbn,
      hokensyaName,
      insuredName,
      sikakuDate: sikakuDate ? numberToDate(sikakuDate) : 0,
      kofuDate: kofuDate ? numberToDate(kofuDate) : 0,
      startDate: startDate ? numberToDate(startDate) : 0,
      endDate: endDate && endDate !== MAX_DATE ? numberToDate(endDate) : 0,
      kogakuKbn: String(kogakuKbn),
      tasukaiYm: tasukaiYm ? formatToYYYYMM(tasukaiYm?.toString()) : "",
      tokureiYm1: tokureiYm1 ? formatToYYYYMM(tokureiYm1?.toString()) : "",
      tokureiYm2: tokureiYm2 ? formatToYYYYMM(tokureiYm2?.toString()) : "",
      genmenKbn: genmenKbn ?? 0,
      syokumuKbn: syokumuKbn ?? 0,
      keizokuKbn: keizokuKbn ?? 0,
      genmenRate: String(genmenRate || ""),
      genmenGaku: String(genmenGaku || ""),
      tokki1,
      tokki2,
      tokki3,
      tokki4,
      tokki5,
      hokenMstKey: comboBoxData.hokenMstAllData.find((item) => {
        if (selectedInsurance && "hokenNo" in selectedInsurance) {
          return (
            item.hokenEdaNo === selectedInsurance?.hokenEdaNo &&
            item.hokenNo === selectedInsurance?.hokenNo
          );
        }
        return item.hokenEdaNo === hokenEdaNo && item.hokenNo === hokenNo;
      })?.key,
      checkDate: confirmDate ? numberToDate(confirmDate) : 0,
      confirmDates: [...(confirmDateList ?? [])],
      file: filingInfModels?.[0],
      ...selectedInsurance,
      hokenId,
      seqNo,
    });
  }, [
    sinDate,
    comboBoxData.hokenMstAllData,
    duplicatedInsuranceValue,
    hokenInfoQuery.data?.getApiInsuranceMstGetHokenInf?.data?.hokenInfList,
    reset,
    selectedInsurance,
  ]);

  useEffect(() => {
    const handleUpdateValues = ({
      hokenId,
      ...rest
    }: InsuranceCardFormType) => {
      if (selectedInsurance?.hokenId === hokenId) {
        setValue("endDate", rest.endDate);
      }
    };
    Emitter.once(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, (rest) =>
      handleUpdateValues(rest),
    );

    return () => {
      Emitter.off(EMITTER_EVENT.UPDATE_INSURANCE_VALUE, (rest) =>
        handleUpdateValues(rest),
      );
    };
  }, [selectedInsurance?.hokenId, setValue]);

  const handleDeleteFile = useCallback(() => {
    setValue("file", undefined);
  }, [setValue]);

  return useMemo(
    () => ({
      methods,
      comboBoxData,
      hokenMstOptions: hokenMstOptionsFiltered,
      kogakuKbnOptions,
      tokkiOptions,
      insuranceValidation,
      setInsuranceValidation,
      handleConfirmValidation,
      onSubmit,
      hokenCheck,
      isLoading: loadingSaveInsurance || loadingValidateInsurance,
      handleUpdatedExpirationDate,
      setFileCard,
      handleDeleteFile,
    }),
    [
      comboBoxData,
      handleConfirmValidation,
      hokenCheck,
      hokenMstOptionsFiltered,
      insuranceValidation,
      kogakuKbnOptions,
      loadingSaveInsurance,
      loadingValidateInsurance,
      methods,
      onSubmit,
      tokkiOptions,
      handleUpdatedExpirationDate,
      setFileCard,
      handleDeleteFile,
    ],
  );
};
