import { WithClinicAuth } from "@/components/functional/WithClinicAuth";
import { NoPermission } from "@/components/ui/NoPermission";
import { ClinicMapContent } from "@/features/clinic-map/ui/ClinicMapContent";
import { PortalInfo } from "@/features/setting-portal/ui/info/PortalInfo";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { usePortal } from "@/providers/PortalProvider";

import type { PortalPreviewData } from "@/features/setting-portal/types";

type PortalInfoPageContentProps = {
  onTogglePreview?: () => void;
  onPreview?: (data: PortalPreviewData) => void;
};

const PortalInfoPageContent = ({
  onTogglePreview,
  onPreview,
}: PortalInfoPageContentProps) => {
  const { hospital, tags, examination, specialist } = usePortal();

  if (!tags || !examination || !specialist) {
    return null;
  }

  return (
    <PortalInfo
      hospital={hospital}
      tags={tags}
      examination={examination}
      specialist={specialist}
      onTogglePreview={onTogglePreview}
      onPreview={onPreview}
    />
  );
};

const PortalInfoPage = () => {
  const { isSessionLoading, pages } = useGetUserPermissions();
  if (isSessionLoading) return null;

  if (!pages.settingPortalEnabled) {
    return (
      <ClinicMapContent>
        <NoPermission />
      </ClinicMapContent>
    );
  }

  return (
    <ClinicMapContent>
      <PortalInfoPageContent />
    </ClinicMapContent>
  );
};

export default WithClinicAuth(PortalInfoPage);
