import React, { useEffect, useState } from "react";

import styled from "styled-components";
import { useFormContext } from "react-hook-form";

import { PatientReservationModal } from "@/components/common/PatientSearch/PatientReservationModal/PatientReservationModal";
import { ContentLoading } from "@/components/ui/ContentLoading";
import { Button } from "@/components/ui/NewButton";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { Flow, useModal } from "@/features/karte/providers/ModalProvider";
import { StyledButton } from "@/features/karte/styles/common";
import { ModalProcessPrintOutPatient } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/ModalProcessPrintOutPatient";
import { ModalWaitingPrescriptionContentCopy } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/ModalWaitingPrescriptionContentCoppy";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import { ErrorPrintSettingModal } from "@/features/karte/ui/Karte/PrintSetting/ErrorPrintSettingModal";
import { ModalPrescriptionCheck } from "@/features/karte/ui/Karte/PrintSetting/ModalPrescriptionCheck";
import { PrintSettingModal } from "@/features/karte/ui/Karte/PrintSetting/ModalPrintSetting";
import { StationPrescription } from "@/features/karte/ui/Karte/StationPrescription";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { RenderIf } from "@/utils/common/render-if";
import { usePostApiDiseasesUpsertMutation } from "@/apis/gql/operations/__generated__/disease";
import { ConnectSocketErrorModal } from "@/features/karte/components/ConnectSocketErrorModal";
import { usePostApiTodayOrdValidateMutation } from "@/apis/gql/operations/__generated__/karte-validate";

import { useGetOrderInfoContext } from "../../hooks/useGetOrderInfoContext";
import { LockAreaType, useLock } from "../../providers/LockInforProvider";
import { ConfirmSaveDraft } from "../KarteModals/ConfirmSaveDraft";
import { SaveMedicalErrorModal } from "../KarteModals/SaveMedicalErrorModal";
import { useKarteFooter } from "../../hooks/useKarteFooter";
import { IndicationDiseaseCheckModal } from "../SimpleKarte/IndicationDiseaseCheck/IndicationDiseaseCheckModal";
import { KarteErrorCode } from "../../constants/error-validate-medical";

import { FlowLocalPrescription } from "./FlowLocalPresciption";
import { ModalRetryCancel } from "./ModalRetryCancel";
import { ModalRepeatProcessResult } from "./RepeatProcessResultModal";
import { useObserverWaitingModalContext } from "./StationPrescription/ObserverWaitingModalActionProvider";
import { ConsultationSaveButton } from "./ConsultationSaveButton";
import { convertFormValueToUpsertParams } from "./MedicineOrder/utils/orderForm";

import type { GetPatientQuery } from "@/apis/gql/operations/__generated__/patient";
import type { AddDiseaseDetailType } from "../../types/diseases";
import type { KarteFormData } from "../../types/karte-order";

const ConsultationBottomContentContainer = styled.div`
  display: flex;
  bottom: 0;
  height: 52px;
  justify-content: space-between;
  width: 100%;
  border-top: 1px solid #e2e3e5;
  border-bottom: 1px solid #e2e3e5;
  align-items: center;
  background-color: #fff;
`;

const CustomStyledButton = styled(StyledButton)`
  color: #243544;
  width: 80px;
  font-weight: normal;
  height: 28px;
  margin: 0px 4px;
`;

const CustomStyleButtonPrintSetting = styled(Button)`
  height: 28px;
  margin-right: 4px;
  width: 80px;
  &:hover {
    color: #79d4ed !important;
    border-color: #79d4ed !important;
    background: #ffffff !important;
    transition: all 0.3s ease-in-out;
  }
`;

const RightBottomContent = styled.div`
  display: flex;
  justify-content: space-between;
  margin-right: 12px;
  flex: auto;
`;

const LeftButtonWrapper = styled.div`
  display: flex;
  padding-left: 6px;
  flex: 0.6;
  justify-content: space-between;
  align-items: center;
`;

const LoadingWrap = styled.div<{ isLoadingActiontreatment?: boolean }>`
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: ${({ isLoadingActiontreatment }) =>
    isLoadingActiontreatment ? "52px" : "100%"};
  position: absolute;
  z-index: 100;
  left: 0;
  right: 0;
  top: ${({ isLoadingActiontreatment }) =>
    isLoadingActiontreatment ? "unset" : "0"};
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
`;

type Props = {
  patient: GetPatientQuery["getPatient"];
};

export const ConsultationBottomContent: React.FC<Props> = ({
  patient,
}: Props) => {
  const { watch } = useFormContext<KarteFormData>();
  const { isLock, setIsLastLock } = useLock();
  const { notification } = useGlobalNotification();
  const { handleCheckGairaiRiha, onModalCheckDiseaseValidate } =
    useKarteFooter();

  const [flowLocalPrescriptionResult, setFlowLocalPrescriptionResult] =
    useState<{
      xmlError?: string | undefined;
      isSuccess?: boolean | undefined;
      isSkip?: boolean | undefined;
    }>();

  const {
    state: {
      confirmSaveDraftOpen,
      errorSaveWhenLockOpen,
      errorSaveDiseaseCheckOpen,
      printSettingsOpen,
      isProcessPrintOutPatient,
      isOpenProcessStationPrescription,
      isOpenProcessFlowLocalPrescription,
      isPrintOutpatientPrescription,
      isOpenCreateRegisterInfo,
      isReservationModalOpen,
      paymentAutoCalculationOpen,
      isErrorConnectionSocket,
    },
    handleOpenModal,
    handleCloseModal,
  } = useModal();

  const {
    ptId,
    sinDate,
    raiinNo,
    raiinStatus,
    insuranceDefault,
    drugDataModal,
    setDrugDataModal,
    setIsOpenRaiinoDeletedErrorModal,
  } = useGetOrderInfoContext();

  const {
    loadingKarteContent,
    loadingActiontreatment,
    setKarteError,
    clearErrors,
    checkItem,
  } = useKarteOrderContext();
  const { currentError } = useObserverWaitingModalContext();

  const {
    isShowModalCreateRegisterInfo,
    isOpenEstimatePaymentModal,
    setIsClosePageKarte,
    setIsOpenAccounting,
    setIsOpenEstimatePaymentModal,
    setPrescriptionIdList,
    setIsShowModalCreateRegisterInfo,
  } = useEPrescriptionContext();

  const [postApiDiseasesUpsertMutation] = usePostApiDiseasesUpsertMutation();
  const [postApiTodayOrdValidateMutation] =
    usePostApiTodayOrdValidateMutation();

  const actionAndTreatmentLockInfor = isLock(LockAreaType.ACTION_AND_TREATMENT);

  const handleKarteFormModeError = () => {
    if (actionAndTreatmentLockInfor) {
      setIsLastLock(true);
    } else {
      setIsLastLock(false);
    }
  };

  const isKarteOrderValid = async () => {
    const isRaiinoDeleted = raiinStatus === 9;
    if (isRaiinoDeleted) {
      setIsOpenRaiinoDeletedErrorModal(true);
      return false;
    }

    const isValid = await checkItem({
      notification: true,
      focusFirstErrorInput: true,
    });
    if (!isValid) return false;

    clearErrors();

    const params = convertFormValueToUpsertParams({
      value: watch(),
      raiinNo,
      ptId,
      sinDate,
      defaultHokenPid: insuranceDefault?.hokenPid,
    });

    const { data } = await postApiTodayOrdValidateMutation({
      variables: {
        emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput:
          params,
      },
    });

    const validationData = data?.postApiTodayOrdValidate?.data ?? {};
    const validationOdrInfs = validationData?.validationOdrInfs;

    const isOdrInfError = !!validationOdrInfs?.length;

    if (isOdrInfError) {
      setKarteError(validationData);

      const isOnlyHokenDeleted = validationOdrInfs?.every(
        (item) => item.status === KarteErrorCode.KohiIdNoExist,
      );

      // if hoken is deleted, it isn't show toast msg
      if (!isOnlyHokenDeleted) {
        notification.error({ message: "処理を実行できませんでした。" });
      }

      return false;
    }

    return true;
  };

  const handleTrialCalculation = async (e: React.MouseEvent<HTMLElement>) => {
    const isOrderValid = await isKarteOrderValid();
    if (!isOrderValid) return;

    setIsOpenEstimatePaymentModal(true);
    onModalCheckDiseaseValidate(
      e,
      () => handleOpenModal("PAYMENT_AUTO_CALCULATION", undefined, Flow.Flow1),
      false,
    );
  };

  const handleAutoCalculation = async () => {
    const isOrderValid = await isKarteOrderValid();
    if (!isOrderValid) return;

    const isCheckGairaiRiha = await handleCheckGairaiRiha();
    if (!isCheckGairaiRiha) return;

    handleOpenModal("PAYMENT_AUTO_CALCULATION", undefined, Flow.Flow1);
  };

  const handlePrintSetting = async () => {
    const isCheckGairaiRiha = await handleCheckGairaiRiha();
    if (!isCheckGairaiRiha) return;

    handleOpenModal("PRINT_SETTING");
    setIsClosePageKarte(false);
    setIsOpenAccounting(false);
  };

  const handleAddIndicationDiseaseCheck = async (
    data: AddDiseaseDetailType[],
  ) => {
    if (data.length > 0) {
      await postApiDiseasesUpsertMutation({
        variables: {
          input: {
            ptDiseases: data,
          },
        },
        onCompleted: () => {
          // TODO: refresh list disease when complete when hook useDisease merge to dev2
          setDrugDataModal([]);
        },
      });
    }
    const modalFlow = isOpenEstimatePaymentModal ? Flow.Flow1 : Flow.Flow2;
    handleCloseModal("INDICATION_DISEASE_CHECK");
    handleOpenModal("PAYMENT_AUTO_CALCULATION", undefined, modalFlow);
  };

  useEffect(() => {
    if (!paymentAutoCalculationOpen) return;
    handleKarteFormModeError();
  }, [paymentAutoCalculationOpen, actionAndTreatmentLockInfor]);

  return (
    <ConsultationBottomContentContainer>
      <RenderIf condition={printSettingsOpen}>
        <PrintSettingModal />
      </RenderIf>

      <RenderIf condition={isPrintOutpatientPrescription}>
        <ModalWaitingPrescriptionContentCopy />
      </RenderIf>

      <RenderIf condition={isErrorConnectionSocket}>
        <ConnectSocketErrorModal />
      </RenderIf>

      <RenderIf condition={isProcessPrintOutPatient}>
        <ModalProcessPrintOutPatient />
      </RenderIf>

      <ModalPrescriptionCheck />
      <ErrorPrintSettingModal />
      <RenderIf condition={isReservationModalOpen}>
        <PatientReservationModal
          isOpen={isReservationModalOpen}
          onClose={() => handleCloseModal("RESERVATION")}
          patient={{
            patientID: Number(patient.patientId),
            patientName: patient.patientName,
            patientNameKana: patient.patientNameKana,
            gender: patient.gender,
            birthdate: patient.birthdate,
          }}
        />
      </RenderIf>
      <RightBottomContent>
        <LeftButtonWrapper>
          <RenderIf condition={isOpenProcessStationPrescription}>
            <StationPrescription
              setFlowLocalPrescriptionResult={({
                xmlError,
                isSuccess,
                isSkip,
              }) => {
                setFlowLocalPrescriptionResult({
                  xmlError: xmlError,
                  isSuccess: isSuccess,
                  isSkip: isSkip,
                });
              }}
              flowLocalPrescriptionResult={flowLocalPrescriptionResult}
            />
          </RenderIf>

          <RenderIf condition={isOpenProcessFlowLocalPrescription}>
            <FlowLocalPrescription
              setFlowLocalPrescriptionResult={({
                xmlError,
                isSuccess,
                isSkip,
              }) => {
                setFlowLocalPrescriptionResult({
                  xmlError: xmlError,
                  isSuccess: isSuccess,
                  isSkip: isSkip,
                });
              }}
              flowLocalPrescriptionResult={flowLocalPrescriptionResult}
            />
          </RenderIf>
          <RenderIf
            condition={
              !!flowLocalPrescriptionResult?.xmlError ||
              !!flowLocalPrescriptionResult?.isSuccess ||
              currentError.current === "ERROR" ||
              currentError.current === "XML"
            }
          >
            <ModalRepeatProcessResult
              error={currentError.current ?? ""}
              errorXML={flowLocalPrescriptionResult?.xmlError}
              isSuccess={flowLocalPrescriptionResult?.isSuccess}
              setFlowLocalPrescriptionResult={setFlowLocalPrescriptionResult}
              flowLocalPrescriptionResult={flowLocalPrescriptionResult}
            />
          </RenderIf>
          <RenderIf condition={isOpenCreateRegisterInfo}>
            <ModalRetryCancel
              isShowModalCreateRegisterInfo={isShowModalCreateRegisterInfo}
              setIsShowModalCreateRegisterInfo={
                setIsShowModalCreateRegisterInfo
              }
              ptId={ptId}
              raiinNo={raiinNo}
              sinDate={sinDate}
              // openModalRetryCancel={!!prescriptionIdList.length}
              // prescriptionIdList={prescriptionIdList}
              handleCloseModal={() => {
                setPrescriptionIdList([]);
                handleCloseModal("CREATE_REGISTER_INFO");
                handleCloseModal("DUPLICATE_MEDICATION_CHECK");
                // handleGetPrescriptionIdList();
              }}
            />
          </RenderIf>

          {/*<CustomStyleButtonPrintSetting*/}
          {/*  onClick={() => handleOpenModal("IS_PROCESS_STATION_PRESCRIPTION")}*/}
          {/*  varient={"ordinary"}*/}
          {/*>*/}
          {/*  remote*/}
          {/*</CustomStyleButtonPrintSetting>*/}

          {/*<CustomStyleButtonPrintSetting*/}
          {/*  onClick={() => handleOpenModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL")}*/}
          {/*  varient={"ordinary"}*/}
          {/*>*/}
          {/*  local*/}
          {/*</CustomStyleButtonPrintSetting>*/}

          {/*<CustomStyleButtonPrintSetting*/}
          {/*  onClick={() =>*/}
          {/*    handleOpenModal("IS_PROCESS_FLOW_LOCAL_PRESCRIPTION")*/}
          {/*  }*/}
          {/*  varient={"ordinary"}*/}
          {/*>*/}
          {/*  show flow local*/}
          {/*</CustomStyleButtonPrintSetting>*/}

          {/*<CustomStyleButtonPrintSetting*/}
          {/*  onClick={() =>*/}
          {/*    handleCloseModal("IS_PROCESS_FLOW_LOCAL_PRESCRIPTION")*/}
          {/*  }*/}
          {/*  varient={"ordinary"}*/}
          {/*>*/}
          {/*  close flow local*/}
          {/*</CustomStyleButtonPrintSetting>*/}

          <CustomStyleButtonPrintSetting
            onClick={handlePrintSetting}
            varient={"ordinary"}
            htmlType="button"
          >
            印刷
          </CustomStyleButtonPrintSetting>
          <CustomStyledButton
            htmlType="button"
            onClick={() => handleOpenModal("RESERVATION")}
          >
            予約
          </CustomStyledButton>
          <CustomStyledButton
            htmlType="button"
            onClick={handleTrialCalculation}
          >
            試算
          </CustomStyledButton>
          <CustomStyledButton htmlType="button" onClick={handleAutoCalculation}>
            算定
          </CustomStyledButton>
        </LeftButtonWrapper>

        <ConsultationSaveButton />
      </RightBottomContent>

      {loadingKarteContent && (
        <LoadingWrap>
          <ContentLoading />
        </LoadingWrap>
      )}

      {loadingActiontreatment && (
        <LoadingWrap isLoadingActiontreatment={loadingActiontreatment} />
      )}

      {drugDataModal && drugDataModal.length > 0 && (
        <IndicationDiseaseCheckModal
          onSubmit={handleAddIndicationDiseaseCheck}
          data={drugDataModal}
        />
      )}

      {confirmSaveDraftOpen && <ConfirmSaveDraft isOpen />}

      {errorSaveWhenLockOpen || errorSaveDiseaseCheckOpen ? (
        <SaveMedicalErrorModal isOpen />
      ) : null}
    </ConsultationBottomContentContainer>
  );
};
