import { type FocusEventHandler } from "react";

import { cloneDeep, isNil, partition } from "lodash";
import { useFormContext } from "react-hook-form";

import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { KarteFormMode } from "@/features/karte/providers/KarteOrderProvider/types";
import { RpId } from "@/features/karte/constants";
import {
  markCheckedRp,
  OrderInforToOrderRp,
} from "@/features/karte/hooks/karteValidations/utils";
import {
  type KarteFormData,
  type OrderRp,
} from "@/features/karte/types/karte-order";

import { getOrderRpKey } from "../utils/formKeys";
import { isSecondaryUsageItem, isUsageItem } from "../utils/orderItem";

import { handleKeyDownSuryo, handlePasteSuryo } from "./utils";

type Props = {
  rpIndex: number;
  itemIndex?: number;
  usageIndex?: number;
};

export type OrderItemErrors = {
  hasSuryoError: boolean;
  handleChangeSuryo: FocusEventHandler<HTMLInputElement>;
  handleKeyDownSuryo: (
    e: React.KeyboardEvent<HTMLInputElement>,
    format?: RegExp,
  ) => void;
  handlePasteSuryo: (e: React.ClipboardEvent<HTMLInputElement>) => void;
};

// todo rename to withKarteValidate
export const withOrderItemErrors = <T extends Props & OrderItemErrors>(
  WrappedComponent: React.FC<T>,
) => {
  return (props: Omit<T, keyof OrderItemErrors>) => {
    const { setValue, getValues } = useFormContext<KarteFormData>();
    const { rpIndex, itemIndex, usageIndex } = props;
    const {
      karteFormMode,
      setLoadingActiontreatment,
      getItemErrors,
      checkDrug,
      checkItem,
      checkSanteiMaxFromKarte,
      getAutoAddItem,
      sortAndSaveRps,
      clearErrors,
    } = useKarteOrderContext();

    // check suryo error
    const orderItemIndex = itemIndex ?? usageIndex;

    const hasSuryoError = isNil(orderItemIndex)
      ? false
      : !!getItemErrors(rpIndex, orderItemIndex)?.suryoError;

    const handleBlurSuryo: FocusEventHandler<HTMLInputElement> = (e) => {
      e.preventDefault();

      // Disable all interactions by adding an overlay
      const overlay = document.createElement("div");
      overlay.style.position = "fixed";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.backgroundColor = "transparent";
      overlay.style.zIndex = "9999";
      overlay.id = "temp-overlay";
      document.body.appendChild(overlay);

      // Process the blur event
      handleValidateSuryo();

      // Remove the overlay after processing is complete
      setTimeout(() => {
        const tempOverlay = document.getElementById("temp-overlay");
        if (tempOverlay) {
          document.body.removeChild(tempOverlay);
        }
      }, 500); // Adjust timeout as needed
    };

    // Update rpId depending on existing rp or new rp
    const updateRpId = (orderRps: OrderRp[], rpId: string | undefined) => {
      const rpWithNewIds = cloneDeep(orderRps);

      // revert the id after we finish check logic
      if (![RpId.TEMP, RpId.FOR_CHECK, RpId.NEW].includes(rpId)) {
        const currentCheckingRPIndex = rpWithNewIds.findIndex(
          (rp) => rp.id === RpId.FOR_CHECK,
        );
        if (currentCheckingRPIndex > -1) {
          rpWithNewIds[currentCheckingRPIndex]!.id = rpId;
        }
      }

      // mark checked in case current RP is a new RP
      markCheckedRp(rpWithNewIds);

      return rpWithNewIds;
    };

    const handleValidateSuryo = async () => {
      if (karteFormMode !== KarteFormMode.Medical) {
        return;
      }

      setLoadingActiontreatment(true);

      try {
        clearErrors();
        const isCheckItemSuccess = await checkItem();

        const rps = getValues().orderRps;
        const rp = rps[rpIndex];

        if (!rp) {
          return;
        }

        const rpId = rp?.id;

        const currentItem = rp.orderItems[itemIndex ?? 0];

        const isDrugItem =
          currentItem.drugKbn > 0 ||
          isUsageItem(currentItem) ||
          isSecondaryUsageItem(currentItem);

        setValue(`${getOrderRpKey(rpIndex)}.id`, RpId.FOR_CHECK);

        const checkSanteiMaxResult = await checkSanteiMaxFromKarte();

        const { addedInfos, odrInfs, checkingInfos } = checkSanteiMaxResult;
        const insertData = [...checkingInfos, ...addedInfos];
        const odrInfsAfterAutoAdd = await getAutoAddItem(insertData, odrInfs);
        const newOrderRps = odrInfsAfterAutoAdd.map(OrderInforToOrderRp);

        // Fire check drug
        if (isCheckItemSuccess && isDrugItem) {
          const [checkingOrderRps, currentOrderRps] = partition(
            newOrderRps,
            (item) => item.id === RpId.FOR_CHECK,
          );
          const checkingOrderRpsWithNewIds = updateRpId(checkingOrderRps, rpId);
          checkDrug({
            checkingOrderRps: checkingOrderRpsWithNewIds,
            currentOrderRps,
          });
        }

        const finalOrderRps = updateRpId(newOrderRps, rpId);

        sortAndSaveRps(finalOrderRps, false);
      } finally {
        setLoadingActiontreatment(false);
      }
    };

    return (
      <WrappedComponent
        {...(props as unknown as T)}
        hasSuryoError={hasSuryoError}
        handleChangeSuryo={handleBlurSuryo}
        handleKeyDownSuryo={handleKeyDownSuryo}
        handlePasteSuryo={handlePasteSuryo}
      />
    );
  };
};
