import React, { use<PERSON>allback, useMemo, useState } from "react";

import { Select } from "antd";
import { isNil } from "lodash";
import { Controller, useFormContext } from "react-hook-form";
import styled from "styled-components";

import { SvgIconArrowDownSelect } from "@/components/ui/Icon/IconArrowDownSelect";
import { SvgIconLock } from "@/components/ui/Icon/IconLock";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useKarteOrderContext } from "@/features/karte/providers/KarteOrderProvider";
import { KarteFormMode } from "@/features/karte/providers/KarteOrderProvider/types";
import { JikanDict, ShinDict } from "@/features/karte/ui/Karte/constants";
import { RenderIf } from "@/utils/common/render-if";
import { useGetUserPermissions } from "@/hooks/useGetUserPermissions";
import { ContentLoading } from "@/components/ui/ContentLoading";

import { withLockArea } from "../../hocs/withLockArea";
import { useConsultationRightContent } from "../../hooks/useConsultationRightContent";
import { useUpdateOrderItemAdoptedMutation } from "../../hooks/useUpdateOrderItemAdoptedMutation";
import { LockAreaType, useLock } from "../../providers/LockInforProvider";
import { LockLayer, StyledButton } from "../../styles/common";
import { Jikan, Syosai } from "../../types/diseases";
import { createOrderItem } from "../../utils/order";
import { ChangeNameModal } from "../KarteModals/ChangeNameModal";
import { ConfirmApplyModal } from "../KarteModals/ConfirmApplyModal";
import { ExpiredModal } from "../KarteModals/ExpiredModal";
import { HokenErrorModal } from "../KarteModals/HokenErrorModal";
import { RaiinoDeletedErrorModal } from "../KarteModals/RaiinoDeletedErrorModal";
import { SameIndicationAndReleaseModal } from "../KarteModals/SameIndicationAndReleaseModal";
import { BulkTenkiUpdateModal } from "../SimpleKarte/InjuredAndStick/BulkTenkiUpdateModal";
import { ConfirmFirstVisitModal } from "../SimpleKarte/InjuredAndStick/ConfirmFirstVisitModal";
import { useConsultationRightContentHoken } from "../../hooks/consultationRightContent/useHoken";
import { useKarteForm } from "../../hooks/consultationRightContent/useKarteForm";
import { useAutoSelectRp } from "../../hooks/consultationRightContent/useAutoSelectRp";

import { AutoAddItemModal } from "./ConsultationRightContent/modals/AutoAddItemModal";
import { SanteiCheckModal } from "./ConsultationRightContent/modals/SanteiCheckModal";
import { MedicineOrderItem } from "./MedicineOrder/MedicineOrderItem";
import { MedicineOrderRP } from "./MedicineOrder/MedicineOrderRP";
import { RemoveRpModal } from "./MedicineOrder/modals/RemoveRpModal";
import { getOrderRpKey } from "./MedicineOrder/utils/formKeys";
import { ChangeSyosaishinModal } from "./ConsultationRightContent/modals/ChangeSyosaishinModal";

import type { ReactNode } from "react";
import type {
  AutoAddItemModalProps,
  ChangeNameModalProps,
  ExpiredModalProps,
  SanteiCheckModalProps,
  ChangeSyosaishinModalProps,
} from "../../hooks/karteValidations/types";
import type { SameIndicationAndReleaseStateType } from "../../types/indication-and-release";
import type { KarteFormData, OrderSearchItem } from "../../types/karte-order";

const ConsultationRightContentContainer = styled.div`
  min-width: 400px;
  width: 100%;
  overflow: auto; // コンテンツが枠を超えた場合にスクロールできるようにする
  background-color: #ffffff;
  padding-right: 6px;
  position: relative;
`;

const GrayContainer = styled.div`
  padding: 14px 8px;
  color: #243544;
  height: 44px;
  background-color: #f1f4f7;
`;

const MedicationContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: calc(100vh - 56px - var(--global-header-height) - 192px);

  &.data-draft {
    height: calc(100vh - 56px - var(--global-header-height) - 192px - 37px);
  }
  &.set {
    padding-top: 8px;
    height: calc(100vh - 56px - var(--global-header-height) - 192px - 117px);
  }
  overflow-y: auto;

  &.customStyleClipboardMode {
    margin-top: 8px;
  }
`;

const MedicineItemContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
`;

const MedicalItemWrap = styled.div<{
  $selectId: boolean;
}>`
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 0 8px;
  border-radius: 6px;
  cursor: pointer;
  border-width: 2px;
  border-style: solid;
  transition: all 0.2s ease-in-out;
  border-color: ${(props) => (props.$selectId ? "#43c3d5" : "#e2e3e5")};
  margin: 0 8px;
`;

const BoxPatternName = styled.div`
  display: flex;
  align-items: center;
  color: #43c3d5;
  margin: 0 8px 8px 0;
  justify-content: space-between;
`;

const LinePatternName = styled.div`
  display: flex;
  align-items: center;
  height: 2px;
  background-color: #43c3d5;
  margin-right: 8px;
  flex: 1;
  min-width: 34%;
  max-width: 87%;
`;

const PatternNameLabel = styled.div`
  font-size: 13px;
  font-family: "Roboto" !important;
  font-weight: bold;
  font-weight: bold;
  text-align: right;
`;

const MedicationGroupLabel = styled.div`
  height: 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  color: #243544;
`;
const TreatmentActionContainer = styled.div`
  display: flex;
  padding: 8px;
  justify-content: flex-start;
`;

const StyleSelect = styled(Select)`
  margin: 0px 2px;
  width: 76px;
  height: 28px;
  .ant-select-selector {
    background-color: #fbfcfe !important;
    padding: 6px 4px 6px 8px !important;
  }
  .ant-select-selection-item {
    font-family: "Roboto" !important;
  }
  &.ant-select-disabled {
    opacity: 0.3;
  }
`;

const CustomStyleButton = styled(StyledButton)`
  height: 28px;
  margin-left: 4px;
  min-width: 76px;
  width: fit-content;
  padding: 7px 8px;
  & > span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    -webkit-line-clamp: 1;
    height: 14px;
    line-height: 1;
  }
`;

const LoadingWrap = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  position: absolute;
  z-index: 100;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
`;

export const ConsultationRightContent: React.FC = () => {
  const { updateOrderItemAdopted } = useUpdateOrderItemAdoptedMutation();
  const [insuranceName, setInsuranceName] = useState<string>("");
  const [loadingChangeInsurance, setLoadingChangeInsurance] = useState(false);

  const { isOpenRaiinoDeletedErrorModal } = useGetOrderInfoContext();

  const {
    selectedRpId,
    removeOrderRp,
    updateItemInRp,
    shouldValidateOrder,

    getRpErrors,
    karteFormMode,
    isDraft,
    loadingActiontreatment,
    isOpenSanteiMaxModal,
    isOpenAutoAddItemModal,
    isOpenExpiredModal,
    isOpenChangeNameModal,
    isOpenChangeSyosaishinModal,
    validateCloseModal,
    validateModalProps,
  } = useKarteOrderContext();

  const { isLock, openRequestUnlockModal, handleLock } = useLock();
  const { watch, setValue } = useFormContext<KarteFormData>();
  const orderRpsWatch = watch("orderRps");

  const {
    isOpenHokenErrorModal,
    isOpenInsurancecCombinationModal,
    isOpenConfirmApplyModal,
    rpHokenNames,
    defaultInsurance,
    highLightHokenNameError,
    showHokenHighlightLine,

    setIsOpenConfirmApplyModal,
    handleCloseConfirmApplyModal,
    setIsOpenHokenErrorModal,
    updateInsuranceModalDisplay,
    handleApplyToAllRp,
    InsurancecCombinationModalWithLock,
  } = useConsultationRightContentHoken();

  const {
    removeRpIndex,
    titleModalDelete,

    onRemoveOrderItem,
    editModalRender,
    handleRemoveRp,
    onCloseRemoveConfirmModal,
  } = useKarteForm();

  const { inViewRef, containerRef, selectedItemRef } = useAutoSelectRp();
  const { functions } = useGetUserPermissions();

  const actionAndTreatmentLockInfor = isLock(LockAreaType.ACTION_AND_TREATMENT);
  const { modalType, openBulkTenkiDateUpdateModal, closeModal } =
    useConsultationRightContent();

  const [sameIndicationOrReleaseModal, setSameIndicationOrReleaseModal] =
    useState<SameIndicationAndReleaseStateType>({
      isOpen: false,
      itemIndex: 0,
      rpIndex: 0,
      yjCode: "",
      modalType: null,
    });

  const handleSubmitModalReleaseAndSameIndication = (
    itemIdication: OrderSearchItem,
  ) => {
    if (!itemIdication) return;
    const orderItem = createOrderItem(itemIdication);
    updateOrderItemAdopted(orderItem);
    updateItemInRp(
      sameIndicationOrReleaseModal.itemIndex,
      sameIndicationOrReleaseModal.rpIndex,
      orderItem,
    );
  };

  const RemoveRpModalWithLock = useMemo(
    () => withLockArea(RemoveRpModal, LockAreaType.ACTION_AND_TREATMENT),
    [],
  );
  const ConfirmFirstVisitModalWithLock = useMemo(
    () =>
      withLockArea(ConfirmFirstVisitModal, LockAreaType.ACTION_AND_TREATMENT),
    [],
  );

  const RenderLockLayer = useCallback((): ReactNode => {
    if (!functions.karteMedicalNotesOperationsEnabled) {
      return <LockLayer isLock />;
    }
    if (karteFormMode !== KarteFormMode.Set) {
      return (
        <LockLayer
          isLock={!!actionAndTreatmentLockInfor}
          onClick={() =>
            openRequestUnlockModal(LockAreaType.ACTION_AND_TREATMENT)
          }
        >
          <div>
            <SvgIconLock />
            <span>{actionAndTreatmentLockInfor?.userName}</span>
          </div>
        </LockLayer>
      );
    }

    return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    actionAndTreatmentLockInfor,
    functions.karteMedicalNotesOperationsEnabled,
    karteFormMode,
  ]);

  return (
    <ConsultationRightContentContainer>
      <RenderLockLayer />

      <GrayContainer>
        <MedicationGroupLabel>処置・行為</MedicationGroupLabel>
      </GrayContainer>

      <RenderIf condition={shouldValidateOrder}>
        <TreatmentActionContainer>
          <Controller
            name="syosaiKbn"
            render={({ field }) => (
              <StyleSelect
                id="syosaiKbn"
                options={ShinDict}
                dropdownStyle={{
                  width: "145px",
                }}
                listHeight={300}
                suffixIcon={<SvgIconArrowDownSelect />}
                {...field}
                onChange={(value) => {
                  if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
                  field.onChange(value);
                  if (value === Syosai.Syosin) {
                    openBulkTenkiDateUpdateModal();
                  }
                }}
              ></StyleSelect>
            )}
          ></Controller>

          <Controller
            name="jikanKbn"
            render={({ field }) => (
              <StyleSelect
                id="jikanKbn"
                options={JikanDict}
                disabled={field?.value === Jikan.UnSet}
                dropdownStyle={{
                  width: "90px",
                }}
                suffixIcon={<SvgIconArrowDownSelect />}
                {...field}
                onChange={(value) => {
                  if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
                  field.onChange(value);
                }}
                value={field?.value === Jikan.UnSet ? undefined : field?.value}
              ></StyleSelect>
            )}
          />

          <CustomStyleButton
            id="hokenSName"
            onClick={updateInsuranceModalDisplay(true)}
          >
            <span>{defaultInsurance?.hokenSName}</span>
          </CustomStyleButton>
        </TreatmentActionContainer>
      </RenderIf>

      <MedicationContainer
        className={`scrollbar ${
          !shouldValidateOrder
            ? karteFormMode === KarteFormMode.Set
              ? KarteFormMode.Set
              : "customStyleClipboardMode"
            : ""
        } ${isDraft ? "data-draft" : ""}`}
        ref={containerRef}
      >
        <MedicineItemContainer>
          {orderRpsWatch.map((rp, rpIndex) => (
            <div key={rp.customId} id={`rp-${rp.customId}`}>
              {showHokenHighlightLine[rpIndex] && (
                <BoxPatternName id="rp-insurance">
                  <LinePatternName />
                  <RenderIf
                    condition={
                      !getRpErrors(rpIndex).invalidHoken &&
                      !getRpErrors(rpIndex).invalidKohis?.length
                    }
                  >
                    <PatternNameLabel>
                      {rpHokenNames[rpIndex] || ""}{" "}
                    </PatternNameLabel>
                  </RenderIf>
                </BoxPatternName>
              )}
              <MedicalItemWrap
                id="rp-container"
                $selectId={selectedRpId === rp.customId}
                // set both ref to component
                ref={(node) => {
                  if (selectedRpId === rp.customId) {
                    selectedItemRef.current = node;
                  }
                  inViewRef(node);
                }}
                data-medical-item="true"
                data-rp-id={rp.customId}
              >
                <MedicineOrderRP
                  rpIndex={rpIndex}
                  isSelected={selectedRpId === rp.customId}
                  item={rp}
                  rpFormKey={getOrderRpKey(rpIndex)}
                  handleRemove={() => {
                    handleRemoveRp(rpIndex);
                  }}
                >
                  {rp.orderItems.map((item, itemIndex) => (
                    <>
                      <MedicineOrderItem
                        key={item.customId + "-" + item.itemName}
                        item={item}
                        rp={rp}
                        rpIndex={rpIndex}
                        itemIndex={itemIndex}
                        handleOpenSameIndicationOrReleaseModal={(modalType) => {
                          setSameIndicationOrReleaseModal({
                            isOpen: true,
                            itemIndex,
                            rpIndex,
                            yjCode: item.yjCd ?? "",
                            modalType,
                          });
                        }}
                        handleRemoveItem={() => {
                          onRemoveOrderItem(itemIndex, rpIndex);
                        }}
                      />
                      {editModalRender(rp, itemIndex, rpIndex)}
                    </>
                  ))}
                </MedicineOrderRP>
              </MedicalItemWrap>
            </div>
          ))}
        </MedicineItemContainer>
      </MedicationContainer>

      {loadingActiontreatment && (
        <LoadingWrap>
          <ContentLoading />
        </LoadingWrap>
      )}

      {!isNil(removeRpIndex) && (
        <RemoveRpModalWithLock
          title={titleModalDelete}
          isOpen={!isNil(removeRpIndex)}
          onClose={onCloseRemoveConfirmModal}
          handleRemoveRp={() => removeOrderRp(removeRpIndex!)}
        />
      )}

      {isOpenInsurancecCombinationModal && (
        <InsurancecCombinationModalWithLock
          isOpen={isOpenInsurancecCombinationModal}
          onClose={updateInsuranceModalDisplay(false)}
          setLoadingChangeInsurance={setLoadingChangeInsurance}
          setInsuranceName={setInsuranceName}
          setIsOpenConfirmApplyModal={setIsOpenConfirmApplyModal}
          orderRpsWatch={orderRpsWatch}
        />
      )}

      {isOpenConfirmApplyModal && !loadingChangeInsurance && (
        <ConfirmApplyModal
          isOpen
          closeModal={handleCloseConfirmApplyModal}
          handleChange={handleApplyToAllRp}
          title="保険組み合わせの変更"
          textSubmitButton="はい"
          textCancelButton="いいえ"
          message={`全てのオーダーの保険組み合わせを「${insuranceName}」に変更しますか？`}
        />
      )}

      {sameIndicationOrReleaseModal.isOpen && (
        <SameIndicationAndReleaseModal
          isOpen
          onClose={() => {
            setSameIndicationOrReleaseModal({
              isOpen: false,
              itemIndex: 0,
              rpIndex: 0,
              yjCode: "",
              modalType: null,
            });
          }}
          title={sameIndicationOrReleaseModal.modalType?.title ?? ""}
          yjCode={sameIndicationOrReleaseModal.yjCode}
          genericOrSameItem={sameIndicationOrReleaseModal.modalType?.id ?? 0}
          onSubmit={handleSubmitModalReleaseAndSameIndication}
        />
      )}

      {isOpenHokenErrorModal && (
        <HokenErrorModal
          isOpen={isOpenHokenErrorModal}
          setOpen={setIsOpenHokenErrorModal}
          error={highLightHokenNameError ?? ""}
          btnOk={"閉じる"}
          title={"確認"}
          subError={"保険を変更してください。"}
        />
      )}

      {isOpenSanteiMaxModal && (
        <SanteiCheckModal
          {...(validateModalProps as SanteiCheckModalProps)}
          isOpen
          closeModal={validateCloseModal}
        />
      )}

      {isOpenExpiredModal && (
        <ExpiredModal
          {...(validateModalProps as ExpiredModalProps)}
          isOpen
          closeModal={validateCloseModal}
        />
      )}

      {isOpenChangeNameModal && (
        <ChangeNameModal
          {...(validateModalProps as ChangeNameModalProps)}
          closeModal={validateCloseModal}
        />
      )}

      {isOpenAutoAddItemModal && (
        <AutoAddItemModal
          {...(validateModalProps as AutoAddItemModalProps)}
          isOpen
          closeModal={validateCloseModal}
        />
      )}

      {isOpenChangeSyosaishinModal && (
        <ChangeSyosaishinModal
          {...(validateModalProps as ChangeSyosaishinModalProps)}
          isOpen
          closeModal={validateCloseModal}
        />
      )}

      {modalType === "FIRST_VISIT_CONFIRM" && (
        <ConfirmFirstVisitModalWithLock
          isOpen={modalType === "FIRST_VISIT_CONFIRM"}
          onClose={closeModal}
          handleSubmit={() => {
            handleLock(LockAreaType.ACTION_AND_TREATMENT);
            setValue("syosaiKbn", Syosai.Syosin);
            openBulkTenkiDateUpdateModal();
          }}
        />
      )}

      {modalType === "TENKI_DATE" && (
        <BulkTenkiUpdateModal
          isOpen={modalType === "TENKI_DATE"}
          onClose={closeModal}
        />
      )}

      {isOpenRaiinoDeletedErrorModal && <RaiinoDeletedErrorModal isOpen />}
    </ConsultationRightContentContainer>
  );
};
