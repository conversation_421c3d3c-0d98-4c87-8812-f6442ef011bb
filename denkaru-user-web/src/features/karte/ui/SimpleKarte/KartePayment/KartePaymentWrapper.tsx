import { useMemo } from "react";

import { useRouter } from "next/router";
import dayjs from "dayjs";

import { AccountingPaymentProvider } from "@/components/common/KartePayment/providers/AccountingPaymentProvider";
import { AccountingQueryProvider } from "@/components/common/KartePayment/providers/AccountingQueryProvider";
import { ModalProvider as PaymentModalProvider } from "@/components/common/KartePayment/providers/ModalProvider";
import { IndicationDiseaseCheckModal } from "@/components/common/KartePayment/ui/modal/IndicationDiseaseCheckModal";
import { IndicationDiseaseEditModal } from "@/components/common/KartePayment/ui/modal/IndicationDiseaseEditModal";
import { PaymentListModal } from "@/components/common/KartePayment/ui/modal/PaymentListModal";
import { PaymentModal } from "@/components/common/KartePayment/ui/modal/PaymentModal";
import { RESERVE_TYPE_ONLINE } from "@/features/karte/constants";
import {
  useGetOrderInfoContext,
  useGetOrderInfoData,
} from "@/features/karte/hooks/useGetOrderInfoContext";
import { useModal } from "@/features/karte/providers/ModalProvider";

import { EstimatePaymentModal } from "./EstimatePayment/EstimatePaymentModal";

export const KartePaymentWrapper = () => {
  const { state, handleOpenModal, handleCloseModal } = useModal();
  const { raiinStatus, dataHeaderInfo, reservationDetail } =
    useGetOrderInfoContext();

  const { ptId, patient } = useGetOrderInfoData();

  const { query } = useRouter();

  const waitNumber = useMemo(() => {
    if ((raiinStatus ?? 0) < RESERVE_TYPE_ONLINE) return;
    return dataHeaderInfo?.uketukeNo?.toString();
  }, [raiinStatus, dataHeaderInfo]);

  const { raiinNo, sinDate } = useMemo(
    () => ({
      raiinNo: query.raiinNo ? String(query.raiinNo) : "",
      sinDate: query.sinDate ? Number(query.sinDate) : 0,
    }),
    [query.raiinNo, query.sinDate],
  );

  return (
    <PaymentModalProvider>
      <AccountingQueryProvider
        raiinNo={raiinNo}
        sinDate={sinDate}
        waitNumber={waitNumber}
        isOnlineReserve={reservationDetail?.reserveType === RESERVE_TYPE_ONLINE}
        patient={{
          ptId: patient?.patientId ?? "",
          ptNum: patient?.ptNum ?? "",
          sex: patient?.gender ?? 0,
          birthday: Number(dayjs(patient?.birthdate).format("YYYYMMDD")),
          name: patient?.patientName ?? "",
          kanaName: patient?.patientNameKana ?? "",
          portalCustomerId: patient?.portalCustomerId,
        }}
      >
        <AccountingPaymentProvider>
          <PaymentModal
            isOpenModal={state.paymentOpen}
            handleOpenPaymentModal={() => handleOpenModal("PAYMENT")}
            handleClosePaymentModal={() => handleCloseModal("PAYMENT")}
          />
          {state.estimatePaymentOpen && (
            <EstimatePaymentModal
              raiinNo={raiinNo}
              sinDate={sinDate}
              ptId={ptId}
              treatmentName={dataHeaderInfo?.kaSname || ""}
            />
          )}
        </AccountingPaymentProvider>
        <PaymentListModal isOpenPaymentModal={state.paymentOpen} />
        <IndicationDiseaseEditModal />
        <IndicationDiseaseCheckModal />
      </AccountingQueryProvider>
    </PaymentModalProvider>
  );
};
