import { ContentLoading } from "@/components/ui/ContentLoading";

import { usePortalStaffCreate } from "../../hooks/usePortalStaffCreate";

import { PortalStaffCreateForm } from "./PortalStaffCreateForm";

type Props = {
  onTogglePreview?: () => void;
  onPreview?: (data: unknown) => void;
};

export const PortalStaffCreate: React.FC<Props> = ({
  onTogglePreview,
  onPreview,
}) => {
  const { loading, hospital } = usePortalStaffCreate();

  if (loading) {
    return <ContentLoading />;
  }

  if (hospital === null || typeof hospital === "undefined") {
    return null;
  }

  return (
    <PortalStaffCreateForm
      isActive={hospital.isActive}
      onTogglePreview={onTogglePreview}
      onPreview={onPreview}
      hospital={hospital}
    />
  );
};
