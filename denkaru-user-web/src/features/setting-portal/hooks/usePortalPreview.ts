import { useCallback, useEffect, useRef, useState } from "react";

import type { PortalPreviewData } from "../types";
import type { RefObject } from "react";

export const usePortalPreview = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
  previewKey?: number,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const messageChannelRef = useRef<MessageChannel | null>(null);

  useEffect(() => {
    const iframeCurrent = iframeRef.current;
    if (!iframeCurrent) {
      return;
    }
    setIsLoading(true);
    let messageChannel: MessageChannel | null = null;

    const handleIframeLoad = () => {
      if (messageChannelRef.current?.port1) {
        messageChannelRef.current.port1.close();
      }

      messageChannel = new MessageChannel();
      messageChannelRef.current = messageChannel;

      const targetOrigin = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL;

      messageChannel.port1.onmessage = (event: MessageEvent) => {
        if (event.data?.type === "READY") {
          setIsConnected(true);
          setIsLoading(false);
        }
      };

      setTimeout(() => {
        if (iframeCurrent?.contentWindow && messageChannel) {
          iframeCurrent.contentWindow.postMessage(
            { type: "CONNECT" },
            targetOrigin ?? "*",
            [messageChannel.port2],
          );
        }
      }, 100);
    };

    iframeCurrent.addEventListener("load", handleIframeLoad);

    if (iframeCurrent.contentDocument?.readyState === "complete") {
      handleIframeLoad();
    }

    const timeoutId = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => {
      clearTimeout(timeoutId);
      if (iframeCurrent) {
        iframeCurrent.removeEventListener("load", handleIframeLoad);
      }

      if (messageChannel?.port1) {
        messageChannel.port1.close();
      }

      setIsConnected(false);
      setIsLoading(true);
      messageChannelRef.current = null;
    };
  }, [iframeRef, previewKey]);

  const postData = useCallback(
    (data: PortalPreviewData) => {
      if (!isConnected || !messageChannelRef.current?.port1) {
        return;
      }

      const safeData = JSON.parse(JSON.stringify(data));
      messageChannelRef.current.port1.postMessage({
        type: "SET_DATA",
        payload: safeData,
      });
    },
    [isConnected],
  );

  return { postData, isLoading, isConnected };
};
