import type { GetPortalHospitalStaffsQuery } from "@/apis/gql/operations/__generated__/portal-staff";
// eslint-disable-next-line import/no-restricted-paths
import type { HospitalInfoPageType } from "@/features/start/types";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { UploadFile } from "antd";

export type Hospital = NonNullable<
  GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
>;

type BusinessTimeFormValue = {
  businessTimeId: number;
  startTime: string;
  endTime: string;
  monFlag: number;
  tueFlag: number;
  wedFlag: number;
  thuFlag: number;
  friFlag: number;
  satFlag: number;
  sunFlag: number;
};

export type TypePortalStationFormValue = {
  stationId: number;
  stationName: string;
  walkingMinute: number | null;
};

export type TypePortalInfoForm = {
  isActive: boolean;
  name: string;
  postCode: string;
  address1: string;
  address2: string;
  telephone: string;
  directorName: string;
  homePage: string;
  mailAddress: string;
  hospitalStations: TypePortalStationFormValue[];
  accessDetail: string;
  descriptionTitle: string;
  description: string;
  files: UploadFile[];
  businessTimes: BusinessTimeFormValue[];
  timelineDescription: string;
  holidayDetail: string;
  isCarpark: boolean;
  carparkDetail: string;
  tagIds: number[];
  examinationIds: number[];
  specialistIds: number[];
  paymentDetails: string;
};

export type PortalPreviewData = Omit<
  NonNullable<
    GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
  >,
  "files"
> & {
  pageType: HospitalInfoPageType;
  files: Array<{
    url: string;
    thumbUrl: string;
  }>;
  examinations: Array<{
    examinationId: number;
    name: string;
    type: number | undefined;
  }>;
  stations: Array<{
    stationDetail: {
      name: string;
    };
  }>;
  tags: Array<{
    tagId: number;
    name: string;
  }>;
  pictures: Array<{
    fileId: number;
    pictureDetail: {
      filepath: string;
    };
  }>;
  specialists: Array<{
    specialistId: number;
    name: string;
  }>;
  hospitalStaffs: Array<
    NonNullable<
      GetPortalHospitalStaffsQuery["getPortalHospitalStaffs"][number]
    > & {
      pictures: Array<{
        pictId: number;
        hospitalStaffId: number;
        pictureDetail: {
          fileName: string;
          filepath: string;
        };
      }>;
    }
  >;
  treatmentCategories: Array<{
    name: string | undefined;
    treatmentCategoryId: string | undefined;
  }>;
  notifications: Array<{
    hospitalNotificationId: number;
    title: string;
    startDate: string | undefined;
  }>;
};
