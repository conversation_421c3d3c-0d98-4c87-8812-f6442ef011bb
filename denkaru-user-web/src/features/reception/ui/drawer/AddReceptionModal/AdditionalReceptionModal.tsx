import { useState } from "react";

import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useReceptionContext } from "@/features/reception/hooks/useReceptionContext";
import { AdditionalReceptionComponent } from "@/components/common/Patient/AddPatient/ReceptionModal/AdditionalReceptionComponent";

import type { ReceptionComponentProps } from "@/types/reception";

export const AdditionalReceptionModal: React.FC<
  Omit<ReceptionComponentProps, "setLoading">
> = ({ open, onClose, ...props }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { setShowDrawer } = useReceptionContext();
  const [isDisabledButtonAdd, setDisabledButtonAdd] = useState<boolean>(false);

  return (
    <StyledModal
      title="追加受付"
      isOpen={open}
      onCancel={onClose}
      width={760}
      footer={[
        <Button key="cancel" shape="round" varient="tertiary" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="save"
          shape="round"
          varient="primary"
          form="add-reception"
          disabled={loading || isDisabledButtonAdd}
          htmlType="submit"
          loading={loading}
        >
          反映
        </Button>,
      ]}
    >
      <AdditionalReceptionComponent
        setLoading={setLoading}
        setDisabledButtonAdd={setDisabledButtonAdd}
        onClose={() => {
          onClose?.();
          setShowDrawer(false);
        }}
        drawerMode
        isEdit={false}
        {...props}
      />
    </StyledModal>
  );
};

const StyledModal = styled(Modal)`
  .ant-modal-body {
    max-height: calc(100vh - 240px);
    overflow: auto;
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 4px;
    }
  }
`;
