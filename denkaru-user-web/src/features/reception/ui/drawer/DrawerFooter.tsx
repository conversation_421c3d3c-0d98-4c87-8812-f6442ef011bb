import { useEffect, useMemo, useState } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { useReceptionContext } from "@/features/reception/hooks/useReceptionContext";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { FileProvider } from "@/providers/FileProvider";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";
import { RenderIf } from "@/utils/common/render-if";
import { numberToDate } from "@/utils/add-patient";
import { AccountingQueryProvider } from "@/components/common/KartePayment/providers/AccountingQueryProvider";
import { PaymentListModal } from "@/components/common/KartePayment/ui/modal/PaymentListModal";
import {
  ModalProvider as PaymentModalProvider,
  useModal as usePaymentModal,
} from "@/components/common/KartePayment/providers/ModalProvider";
import { GotoMedicalFrom } from "@/constants/renkei";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { useDrawerModal } from "../../hooks/useDrawerModal";
import { useGetTasks } from "../../hooks/useGetTasks";
import { usePrintLedger } from "../../hooks/usePrintLedger";

import { AdditionalReceptionModal } from "./AddReceptionModal/AdditionalReceptionModal";
import { FileModal } from "./FileModal";
import { ListTaskModal } from "./ListTaskModal/ListTaskModal";
import { PrintLedgerModal } from "./PrintLedgerModal";
import { PrintSurveyQrModal } from "./PrintSurveyQrModal";

import type { FC } from "react";
import type {
  DomainModelsPatientInforCheckDrawerLedgerDataExistedModel,
  DomainModelsPatientInforPatientInforModel,
} from "@/apis/gql/generated/types";

const FooterWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  background-color: #f1f4f7;
`;

const StyledButton = styled(Button)`
  width: 150px;
`;

type Props = {
  patientDetail?: DomainModelsPatientInforPatientInforModel;
};

export const DrawerFooter: FC<Props> = ({ patientDetail }) => {
  const { handleError } = useErrorHandler();
  const [isShowDocumentPrint, setIsShowDocumentPrint] =
    useState<DomainModelsPatientInforCheckDrawerLedgerDataExistedModel>({});

  const { state, handleOpenModal, handleCloseModal } = useDrawerModal();
  const {
    state: { selectedReception, selectedSinDate },
  } = useReceptionContext();
  const {
    handleOpenModal: openModalPatientContext,
    handleSetPatient,
    handleSetPatientReservationProps,
  } = usePatientContext();

  const { setSelectedInsurance, resetAllData } = usePatientContext();
  const { handleAuditLogMutation } = useAuditLog();

  const kartePath = `/karte/${selectedReception?.ptId}?sinDate=${selectedSinDate}&raiinNo=${selectedReception?.raiinNo}&openPatientInfo=true&from=${GotoMedicalFrom.PatientInfo}`;

  const { loadingCheckDrawerExisted, checkDrawerExisted } = usePrintLedger();
  const { refetch } = useGetTasks();

  const handleCheckDrawerExisted = async () => {
    handleAuditLogMutation({
      ptId: selectedReception?.ptId,
      sinDate: selectedReception?.sinDate,
      eventCd: AuditEventCode.ReceptionListPrintFormDisplay,
    });
    const data = await checkDrawerExisted({
      ptId: selectedReception?.ptId ?? "",
      raiinNo: selectedReception?.raiinNo ?? "",
      sinDate: selectedReception?.sinDate ?? 0,
    });
    setIsShowDocumentPrint({
      ...data,
    });
    handleOpenModal("PRINT_LEDGER");
  };

  const fileMenuDisabled = useMemo(() => {
    if (!selectedReception) return true;
    const { ptNum = 0, status = 0 } = selectedReception;
    return Number(ptNum) === 0 && [0, 1].includes(Number(status));
  }, [selectedReception]);

  const isInvalidPtNum = useMemo(() => {
    if (!patientDetail) return false;
    return !patientDetail?.ptNum;
  }, [patientDetail]);

  const handleOpenReservationModal = () => {
    if (!patientDetail) {
      return;
    }
    handleSetPatientReservationProps({
      treatmentDepartmentId: selectedReception?.treatmentDepartmentId,
    });

    handleSetPatient({
      patientID: Number(patientDetail?.ptId ?? 0),
      patientName: patientDetail.name,
      patientNameKana: patientDetail.kanaName,
      birthdate: patientDetail?.birthday
        ? numberToDate(patientDetail.birthday).toISOString()
        : undefined,
      email: patientDetail?.mail,
      gender: patientDetail?.sex,
      homePost: patientDetail?.homePost,
      homeAddress1: patientDetail?.homeAddress1,
      homeAddress2: patientDetail?.homeAddress2,
      phoneNumber1: patientDetail?.tel1,
      phoneNumber2: patientDetail?.tel2,
    });

    openModalPatientContext("RESERVATION");
  };

  useEffect(() => {
    if (!patientDetail) {
      return handleSetPatient(null);
    }
  }, [handleSetPatient, patientDetail]);

  return (
    <>
      <FooterWrapper>
        <StyledButton
          varient="ordinary"
          onClick={() => {
            window.open(
              `/karte/${selectedReception?.ptId}?sinDate=${selectedSinDate}&raiinNo=${selectedReception?.raiinNo}&from=${GotoMedicalFrom.Other}`,
              "_blank",
            );
          }}
        >
          カルテ
        </StyledButton>
        <StyledButton
          varient="ordinary"
          onClick={() => {
            window.open(kartePath, "_blank");
          }}
        >
          患者情報
        </StyledButton>
        <StyledButton
          varient="ordinary"
          onClick={() => {
            window.open(
              `/karte/${selectedReception?.ptId}?sinDate=${selectedSinDate}&raiinNo=${selectedReception?.raiinNo}&leftSide=consultation-results&from=${GotoMedicalFrom.Consultation}`,
              "_blank",
            );
          }}
        >
          問診結果
        </StyledButton>
        <PaymentModalProvider>
          <AccountingQueryProvider
            raiinNo={selectedReception?.raiinNo || ""}
            sinDate={selectedSinDate}
            waitNumber={selectedReception?.uketukeNo?.toString()}
            patient={{
              ptId: patientDetail?.ptId ?? "",
              ptNum: patientDetail?.ptNum ?? "",
              sex: patientDetail?.sex ?? 0,
              birthday: Number(patientDetail?.birthday),
              name: patientDetail?.name ?? "",
              kanaName: patientDetail?.kanaName ?? "",
              portalCustomerId: patientDetail?.portalCustomerId,
            }}
          >
            <PaymentButton status={selectedReception?.status} />
            <PaymentListModal />
          </AccountingQueryProvider>
        </PaymentModalProvider>
        <StyledButton varient="ordinary" onClick={handleOpenReservationModal}>
          次回予約
        </StyledButton>
        <StyledButton
          varient="ordinary"
          disabled={isInvalidPtNum}
          onClick={() => {
            if (!selectedReception?.name || !selectedReception.birthday) {
              handleError({
                commonMessage:
                  "受付には「氏名」と「生年月日」の入力が必要です。",
                error: new Error(
                  "受付には「氏名」と「生年月日」の入力が必要です。",
                ),
              });
              return;
            }
            handleOpenModal("ADDITIONAL_RECEPTION");
            handleAuditLogMutation({
              ptId: selectedReception?.ptId,
              sinDate: selectedReception?.sinDate,
              eventCd: AuditEventCode.ReceptionListAdditionalReceptionDisplay,
            });
          }}
        >
          追加受付
        </StyledButton>
        <StyledButton
          varient="ordinary"
          onClick={() => {
            handleOpenModal("FILE");
            handleAuditLogMutation({
              ptId: selectedReception?.ptId,
              eventCd: AuditEventCode.ReceptionListFileDisplay,
            });
          }}
          disabled={fileMenuDisabled || isInvalidPtNum}
        >
          ファイル
        </StyledButton>
        <StyledButton
          varient="ordinary"
          onClick={handleCheckDrawerExisted}
          disabled={loadingCheckDrawerExisted}
        >
          帳票印刷
        </StyledButton>
        <StyledButton
          varient="ordinary"
          onClick={() => {
            refetch();
            handleOpenModal("LIST_TASK");
          }}
        >
          タスク
        </StyledButton>
      </FooterWrapper>
      <PrintSurveyQrModal
        isOpen={state.printSurveyQrOpen}
        onClose={() => {
          handleCloseModal("PRINT_SURVEY_QR");
        }}
      />
      <ListTaskModal
        isOpen={state.listTaskOpen}
        data={selectedReception}
        onClose={() => handleCloseModal("LIST_TASK")}
      />
      <PrintLedgerModal
        isOpen={state.printLedgerOpen}
        receptionRowData={selectedReception!}
        onClose={() => handleCloseModal("PRINT_LEDGER")}
        options={isShowDocumentPrint}
      />
      <RenderIf condition={state.fileOpen}>
        <FileProvider>
          <FileModal
            receptionRowData={selectedReception}
            isOpen={state.fileOpen}
            onClose={() => handleCloseModal("FILE")}
          />
        </FileProvider>
      </RenderIf>
      {state.additionalReceptionOpen && (
        <AdditionalReceptionModal
          open={state.additionalReceptionOpen}
          onClose={() => {
            handleCloseModal("ADDITIONAL_RECEPTION");
            setSelectedInsurance(null);
            resetAllData();
          }}
          ptId={selectedReception?.ptId}
          raiinNo={selectedReception?.raiinNo}
        />
      )}
    </>
  );
};

const PaymentButton = ({ status }: { status?: number }) => {
  const { handleOpenModal: handleOpenPaymentModal } = usePaymentModal();

  const buttonDisabled = useMemo(() => {
    if (status === undefined) return true;
    return status < 5 || status === 9;
  }, [status]);

  return (
    <StyledButton
      varient="ordinary"
      disabled={buttonDisabled}
      onClick={() => handleOpenPaymentModal("PAYMENT_LIST")}
    >
      会計一覧
    </StyledButton>
  );
};
