import { useCallback, useState } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { RenderIf } from "@/utils/common/render-if";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { numberToDate } from "@/utils/add-patient";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";
import { useReceptionValidate } from "@/features/reception/hooks/useReceptionValidate";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { PatientErrorModal } from "@/components/common/Patient/AddPatient/PatientErrorModal";
import { useGetOnlineConfirmationHistoryById } from "@/hooks/add-patient/useGetOnlineConfirmationHistoryById";
import { useGetApiSystemConfGetLazyQuery } from "@/apis/gql/operations/__generated__/system-settings";

import { formatHHmm } from "../../utils";

import type { DomainModelsReceptionReceptionForViewDto } from "@/apis/gql/generated/types";

const CustomBtn = styled(Button)`
  width: 80px;
  height: 28px;
`;

export const ReceptionTime: React.FC<
  DomainModelsReceptionReceptionForViewDto
> = ({
  uketukeTime: receptionTime,
  status,
  ptNum = "0",
  onlineConfirmationId,
  ...props
}) => {
  const {
    handleOpenModal,
    handleUpdateOnlineMasterData,
    changeConfirmingType,
    handleSetPatientProps,
    handleSetPatient,
  } = usePatientContext();
  const { handleAuditLogMutation } = useAuditLog();
  const { checkReceptionValidate } = useReceptionValidate();
  const [errorModalContent, setErrorModalContent] = useState<{
    contentText?: string;
    headingText: string;
    questionText: string;
  }>();

  const { handleError } = useErrorHandler();

  const { refetch: refetchListConfirmOnlineHistory } =
    useGetOnlineConfirmationHistoryById({
      skip: true,
    });

  const [handleGetSystemSetting] = useGetApiSystemConfGetLazyQuery({
    variables: {
      grpCd: 100029,
      grpEdaNo: 201,
    },
  });

  const handlePatientRegistration = () => {
    handleSetPatientProps({
      patientId: props.ptId,
      onlineConfirmationHistoryId: onlineConfirmationId?.toString(),
      raiinNo: props.raiinNo,
      isEdit: true,
    });
    if (props.ptId) {
      handleSetPatient({
        patientID: Number(props.ptId),
        portalCustomerId: Number(props.portalCustomerId),
      });
    }
    if (onlineConfirmationId) {
      changeConfirmingType("ADDING_PATIENT_MY_CARD");
    }
    handleAuditLogMutation({
      eventCd: AuditEventCode.ReceptionListNewPatientRegistrationDisplay,
    });
    handleOpenModal("NEW_PATIENT");
  };

  const handleReception = async () => {
    const isValid = await checkReceptionValidate({
      ptId: props.ptId,
      sinDate: props.sinDate,
      raiinNo: props.raiinNo,
    });
    if (!isValid) {
      return;
    }

    handleSetPatient({
      patientID: Number(props.ptId),
      patientName: props?.name,
      patientNameKana: props?.kanaName,
      birthdate: props?.birthday
        ? numberToDate(props.birthday).toISOString()
        : "",
      gender: props?.sex === "M" ? 1 : 2,
      patientNumber: Number(ptNum),
    });

    const isNoNameOrBirthday = !props.name || !props.birthday;
    handleSetPatientProps({
      patientId: props.ptId,
      onlineConfirmationHistoryId: onlineConfirmationId?.toString(),
      raiinNo: props.raiinNo,
      initialStep: isNoNameOrBirthday ? "CREATE_PATIENT" : "CREATE_RECEPTION",
      isEdit: true,
    });

    if (isNoNameOrBirthday) {
      handleError({
        error: new Error("受付には「氏名」と「生年月日」の入力が必要です。"),
        commonMessage: "受付には「氏名」と「生年月日」の入力が必要です。",
      });
      return handleOpenModal("NEW_PATIENT");
    }

    if (!onlineConfirmationId) {
      handleAuditLogMutation({
        eventCd: AuditEventCode.ReceptionListReceptionDisplay,
        ptId: props.ptId,
        sinDate: props.sinDate,
      });
      return handleOpenModal("NEW_PATIENT");
    }
    changeConfirmingType("ADDING_RECEPTION_MY_CARD");
    handleUpdateOnlineMasterData({
      ptId: Number(props.ptId),
      onlineConfirmHistoryId: Number(onlineConfirmationId),
    });
  };

  const handleGetOnlineConfirmationHistory = async ({
    type,
  }: {
    type: "PATIENT_REGISTRATION" | "RECEPTION";
  }) => {
    const res = await refetchListConfirmOnlineHistory({
      onlineConfirmationHisId: onlineConfirmationId,
    });
    const dataRes =
      res.data.getApiOnlineGetListOnlineConfirmationHistoryModelById?.data
        ?.onlineConfirmationHistoryList?.[0];

    const openModal = () => {
      if (type === "PATIENT_REGISTRATION") {
        handlePatientRegistration();
      }

      if (type === "RECEPTION") {
        handleReception();
      }
    };

    if (!dataRes?.pmhStatus) {
      openModal();
      return;
    }

    const systemSetting = await handleGetSystemSetting();
    const val = systemSetting?.data?.getApiSystemConfGet?.data?.data?.val;

    if (dataRes.pmhStatus > 1 && val === 1) {
      // confirm online my card success but PMH error
      setErrorModalContent({
        contentText:
          dataRes.pmhResult && dataRes.pmhStatus === 3
            ? dataRes.pmhResult
            : undefined,
        headingText: "医療費助成情報の取得に失敗しました",
        questionText:
          type === "PATIENT_REGISTRATION"
            ? "このまま患者登録を続けますか？"
            : "このまま受付を続けますか？",
      });
      return;
    }

    openModal();
  };

  const RenderButton = useCallback(() => {
    if ([0, 1].includes(status!)) {
      return (
        <>
          <RenderIf condition={Number(ptNum) === 0 && props.typeAlert !== 2}>
            <CustomBtn
              varient="standard-sr"
              size="small"
              onClick={() =>
                handleGetOnlineConfirmationHistory({
                  type: "PATIENT_REGISTRATION",
                })
              }
            >
              患者登録
            </CustomBtn>
          </RenderIf>
          <RenderIf condition={Number(ptNum) > 0}>
            <CustomBtn
              varient="standard-sr"
              size="small"
              onClick={() =>
                handleGetOnlineConfirmationHistory({ type: "RECEPTION" })
              }
            >
              受付
            </CustomBtn>
          </RenderIf>
        </>
      );
    } else if (status! >= 2) {
      return <p>{receptionTime && formatHHmm(receptionTime)}</p>;
    } else {
      return;
    }
  }, [
    changeConfirmingType,
    checkReceptionValidate,
    handleAuditLogMutation,
    handleOpenModal,
    handleSetPatient,
    handleSetPatientProps,
    handleUpdateOnlineMasterData,
    onlineConfirmationId,
    props.birthday,
    props?.kanaName,
    props?.name,
    props.ptId,
    props.raiinNo,
    props?.sex,
    props.sinDate,
    props.typeAlert,
    ptNum,
    receptionTime,
    status,
  ]);
  return (
    <div>
      <RenderButton />
      <RenderIf condition={!!errorModalContent}>
        <PatientErrorModal
          isOpen={!!errorModalContent}
          onClose={() => setErrorModalContent(undefined)}
          contentText={errorModalContent?.contentText}
          headingText={errorModalContent?.headingText}
          questionText={errorModalContent?.questionText}
          onCloseText="キャンセル"
          onSubmit={() => {
            if (Number(ptNum) === 0 && props.typeAlert !== 2) {
              handlePatientRegistration();
            }

            if (Number(ptNum) > 0) {
              handleReception();
            }
            setErrorModalContent(undefined);
          }}
        />
      </RenderIf>
    </div>
  );
};
