/* eslint-disable import/no-restricted-paths */
import React, { Children, cloneElement, useRef, useState } from "react";

import styled from "styled-components";

import { useGetPortalStaff } from "@/features/portal/hooks/useGetPortalStaff";
import { MobilePreview } from "@/features/setting-portal/ui/info/MobilePreview";
import { usePortal } from "@/providers/PortalProvider";

import type { PortalPreviewData } from "@/features/setting-portal/types";

const RightContentContainer = styled.div`
  display: flex;
  gap: 20px;
  padding-bottom: 20px;
`;

const Wrapper = styled.div`
  width: 860px;
  background-color: #fff;
  border-radius: 12px;
`;

export const RightContent: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { staffs } = useGetPortalStaff();
  const { hospital } = usePortal();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [previewKey, setPreviewKey] = useState(0);
  const [previewData, setPreviewData] = useState<PortalPreviewData | null>(
    null,
  );

  const handleTogglePreview = () => {
    setIsPreviewing(true);
    setPreviewKey((prev) => prev + 1);
  };

  const handlePreview = (data: PortalPreviewData) => {
    setPreviewData(data);
  };

  const childrenWithProps = Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return cloneElement(
        child as React.ReactElement<{
          iframeRef?: React.RefObject<HTMLIFrameElement | null>;
          onTogglePreview?: () => void;
          onPreview?: (data: PortalPreviewData) => void;
          previewKey?: number;
        }>,
        {
          iframeRef,
          onTogglePreview: handleTogglePreview,
          onPreview: handlePreview,
          previewKey,
        },
      );
    }
    return child;
  });

  if (
    staffs === null ||
    typeof staffs === "undefined" ||
    hospital === null ||
    typeof hospital === "undefined"
  ) {
    return null;
  }

  return (
    <RightContentContainer>
      <Wrapper>{childrenWithProps}</Wrapper>
      <MobilePreview
        hospital={hospital}
        iframeRef={iframeRef}
        isPreviewing={isPreviewing}
        previewKey={previewKey}
        previewData={previewData ?? undefined}
      />
    </RightContentContainer>
  );
};
