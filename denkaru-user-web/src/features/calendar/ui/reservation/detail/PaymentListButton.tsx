import styled from "styled-components";
import dayjs from "dayjs";

import { Button } from "@/components/ui/NewButton";
import { AccountingQueryProvider } from "@/components/common/KartePayment/providers/AccountingQueryProvider";
import {
  ModalProvider,
  useModal,
} from "@/components/common/KartePayment/providers/ModalProvider";
import { PaymentListModal } from "@/components/common/KartePayment/ui/modal/PaymentListModal";
import { useCalendar } from "@/hooks/useCalendar";

import type { Patient } from "@/apis/gql/generated/types";

const StyledButton = styled(Button)`
  width: 80px;
  height: 26px;
  span {
    color: #243544;
  }
`;

type Props = {
  patient?: Patient;
};

const Wrapper = ({ patient }: Props) => {
  return (
    <ModalProvider>
      <AccountingQueryProvider
        raiinNo=""
        sinDate={Number(dayjs().format("YYYYMMDD"))}
        patient={{
          ptId: patient?.patientID.toString() ?? "",
          ptNum: patient?.patientNumber?.toString() ?? "",
          sex: patient?.gender ?? 0,
          birthday: Number(dayjs(patient?.birthdate).format("YYYYMMDD")),
          name: patient?.patientName ?? "",
          kanaName: patient?.patientNameKana ?? "",
          portalCustomerId: patient?.portalCustomerId,
        }}
      >
        <PaymentListButton />
        <PaymentListModal />
      </AccountingQueryProvider>
    </ModalProvider>
  );
};

const PaymentListButton = () => {
  const { setShowReservationDetail } = useCalendar();
  const { handleOpenModal } = useModal();
  return (
    <StyledButton
      varient="ordinary"
      onClick={() => {
        setShowReservationDetail(false);
        handleOpenModal("PAYMENT_LIST");
      }}
    >
      会計一覧
    </StyledButton>
  );
};

export { Wrapper as PaymentListButton };
