import React from "react";

import styled, { css } from "styled-components";

import { PaymentType } from "@/constants/payment";
import { usePaymentModal } from "@/hooks/usePaymentModalProvider";

import { PaymentFailureModal } from "../../payment/error/PaymentFailureModal";
import { RequestPaymentForm } from "../../payment/request/RequestPaymentForm";
import { PaymentSuccessModal } from "../../payment/success/PaymentSuccessModal";

import { PaymentListButton } from "./PaymentListButton";

import type { ReservationDetail } from "@/apis/gql/generated/types";

const PaymentDetail = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #e2e3e5;
  padding-top: 8px;
  line-height: 1;
`;

const PaymentDetailLabel = styled.div`
  display: flex;
  align-items: center;
  margin-right: 4px;
  color: #6a757d;
`;

const PaymentDetailItem = styled.div`
  display: flex;
  align-items: center;
`;

const PaymentAmount = styled.p``;

const PriceText = styled.span`
  font-size: 20px;
  font-weight: bold;
  margin-right: 8px;
  margin-left: 8px;
`;

const PaymentStatusText = styled.span<{ $paymentStatus: number }>`
  ${({ $paymentStatus }) =>
    $paymentStatus === 3 &&
    css`
      color: #e74c3c;
    `}
`;

type Props = {
  reservation: ReservationDetail;
};

const getPaymentStatusText = (paymentStatus: number) => {
  if (paymentStatus === 1) {
    return "未請求";
  }

  if (paymentStatus === 2) {
    return "請求済";
  }

  if (paymentStatus === 3) {
    return "決済エラー";
  }

  return "　";
};

export const ReservationPayment: React.FC<Props> = ({ reservation }) => {
  const { state: modal, handleCloseModal } = usePaymentModal();
  const { patient } = reservation;

  if (!reservation.fincodeCustomerId) {
    return null;
  }

  return (
    <>
      {reservation.paymentDetail ? (
        <PaymentDetail>
          <PaymentDetailItem>
            <PaymentDetailLabel>請求金額</PaymentDetailLabel>
            <PaymentAmount>
              <PriceText>
                {reservation.paymentDetail.paymentType === PaymentType.NO
                  ? "-"
                  : reservation.paymentDetail.billingAmount.toLocaleString()}
              </PriceText>
              円（
              <PaymentStatusText
                $paymentStatus={reservation.paymentDetail.paymentStatus}
              >
                {reservation.paymentDetail.paymentType === PaymentType.NO
                  ? "請求なし"
                  : getPaymentStatusText(
                      reservation.paymentDetail.paymentStatus,
                    )}
              </PaymentStatusText>
              ）
            </PaymentAmount>
          </PaymentDetailItem>
          {patient && <PaymentListButton patient={patient} />}
        </PaymentDetail>
      ) : (
        <RequestPaymentForm reservation={reservation} />
      )}
      {modal.paymentSuccessOpen && (
        <PaymentSuccessModal
          patientId={reservation.patient?.patientID}
          isOpen={modal.paymentSuccessOpen}
          onClose={() => handleCloseModal("PAYMENT_SUCCESS")}
        />
      )}
      {modal.paymentFailureOpen && (
        <PaymentFailureModal
          isOpen={modal.paymentFailureOpen}
          onClose={() => {
            handleCloseModal("PAYMENT_FAILURE");
          }}
          errorMessage={modal.errorMessage}
        />
      )}
    </>
  );
};
