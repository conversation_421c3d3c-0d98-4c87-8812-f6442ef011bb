import { useEffect, useState } from "react";

import styled from "styled-components";

import { useUpdatePrescriptionReceptionMemoMutation } from "@/apis/gql/operations/__generated__/pharmacy-prescription";
import { TextAreaInput } from "@/components/ui/TextAreaInput";
import { useDebounce } from "@/hooks/useDebounce";

import type { PrescriptionReceptionsTableDataType } from "../../types/table";

const StyledTextAreaInput = styled(TextAreaInput)`
  width: 276px;
  height: 86px !important;
  resize: none !important;
`;

type Props = {
  record: PrescriptionReceptionsTableDataType;
};

export const MemoInput: React.FC<Props> = ({ record }) => {
  const [inputText, setInputText] = useState<string | undefined>(undefined);
  const debouncedInput = useDebounce(inputText);

  const [saveMemo] = useUpdatePrescriptionReceptionMemoMutation();

  const handleInputText = (input: string) => {
    setInputText(input);
  };

  useEffect(() => {
    if (
      typeof inputText === "undefined" ||
      typeof debouncedInput === "undefined"
    ) {
      return;
    }

    saveMemo({
      variables: {
        input: {
          prescriptionReceptionId: record.prescriptionReceptionId,
          memo: debouncedInput,
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedInput]);

  return (
    <StyledTextAreaInput
      defaultValue={record.memo}
      maxLength={200}
      onChange={(e) => {
        handleInputText(e.target.value);
      }}
    />
  );
};
