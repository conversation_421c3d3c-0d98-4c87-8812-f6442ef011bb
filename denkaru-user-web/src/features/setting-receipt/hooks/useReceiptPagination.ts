import { useCallback, useState, useMemo } from "react";

import { ApolloError } from "@apollo/client";

import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";
import { usePostApiReceiptGetListMutation } from "@/apis/gql/operations/__generated__/receipt";

import { convertFormInputToSearchInput } from "../utils/search-criteria";

import type { Receipt } from "../types/list";
import type { SearchCriteriaFormType } from "../types/search-criteria";

export type PaginationState = {
  currentPage: Receipt[];
  allData: Receipt[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  error: string | null;
  totalCount: number;
  displayTensu: number;
};

export type SortConfig = {
  sortKey: number; // 0: 患者番号, 1: 氏名, 2: レセプト種別, 3: 最終来院日
  sortOrder: boolean; // true: 昇順, false: 降順
};

export type FilterConfig = {
  filterType: number; // 0: すべて, 1: 社保, 2: 国保, 3: その他
};

export type PaginationConfig = {
  limit: number;
  sort: SortConfig;
  filter: FilterConfig;
};

const DEFAULT_PAGINATION_CONFIG: PaginationConfig = {
  limit: 12,
  sort: {
    sortKey: 0, // 患者番号
    sortOrder: true, // 昇順
  },
  filter: {
    filterType: 0, // すべて
  },
};

export const useReceiptPagination = (
  baseSearchCriteria: SearchCriteriaFormType,
) => {
  const { handleError } = useErrorHandler();
  const [getReceiptList] = usePostApiReceiptGetListMutation();

  const [state, setState] = useState<PaginationState>({
    currentPage: [],
    allData: [],
    isLoading: false,
    isLoadingMore: false,
    hasNextPage: true,
    error: null,
    totalCount: 0,
    displayTensu: 0,
  });

  const [config, setConfig] = useState<PaginationConfig>(
    DEFAULT_PAGINATION_CONFIG,
  );

  // Store current search criteria to use in API calls
  const [currentSearchCriteria, setCurrentSearchCriteria] =
    useState<SearchCriteriaFormType>(baseSearchCriteria);

  // Get cursor values from last record for next page
  const getLastRecordCursor = useCallback(() => {
    const lastRecord = state.allData[state.allData.length - 1];

    if (!lastRecord) {
      return {};
    }

    const cursor = {
      cursorPtId: lastRecord.ptId,
      cursorSinYm: lastRecord.sinYm,
      cursorHokenId: lastRecord.hokenId,
    };

    return cursor;
  }, [state.allData]);

  // Create search input with pagination parameters
  const createSearchInput = useCallback(
    (
      isFirstPage: boolean = false,
      overrideCriteria?: SearchCriteriaFormType,
    ) => {
      const criteriaToUse = overrideCriteria || currentSearchCriteria;

      console.log("🔧 createSearchInput called:", {
        isFirstPage,
        currentSeikyuYm: criteriaToUse.seikyuYm?.format("YYYY/MM"),
        configLimit: config.limit,
        configFilterType: config.filter.filterType,
        usingOverride: !!overrideCriteria,
      });

      const paginationParams = {
        limit: config.limit,
        filterType: config.filter.filterType,
        sortKey: config.sort.sortKey,
        sortOrder: config.sort.sortOrder,
        ...(isFirstPage ? {} : getLastRecordCursor()),
      };

      const searchInput = convertFormInputToSearchInput({
        ...criteriaToUse,
        ...paginationParams,
      });

      console.log("🔧 createSearchInput result:", {
        seikyuYm: searchInput.seikyuYm,
        limit: searchInput.limit,
        filterType: searchInput.filterType,
        isAdvanceSearch: searchInput.isAdvanceSearch,
      });

      return searchInput;
    },
    [currentSearchCriteria, config, getLastRecordCursor],
  );

  // Load first page
  const loadFirstPage = useCallback(
    async (overrideCriteria?: SearchCriteriaFormType) => {
      console.log("📄 loadFirstPage called with override:", {
        hasOverride: !!overrideCriteria,
        overrideSeikyuYm: overrideCriteria?.seikyuYm?.format("YYYY/MM"),
      });

      setState((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
        allData: [],
        currentPage: [],
        hasNextPage: true,
      }));

      try {
        const queryInput = createSearchInput(true, overrideCriteria);
        console.log("📄 loadFirstPage - about to call API with queryInput:", {
          seikyuYm: queryInput.seikyuYm,
          limit: queryInput.limit,
          filterType: queryInput.filterType,
        });

        const response = await getReceiptList({
          variables: { input: queryInput },
        });

        const receiptList =
          response.data?.postApiReceiptGetList?.data?.receiptList ?? [];
        const totalCount =
          response.data?.postApiReceiptGetList?.data?.totalCount ?? 0;
        const displayTensu =
          response.data?.postApiReceiptGetList?.data?.displayTensu ?? 0;

        console.log("📄 loadFirstPage - API response received:", {
          receiptListLength: receiptList.length,
          totalCount,
          displayTensu,
        });

        setState((prev) => ({
          ...prev,
          isLoading: false,
          currentPage: receiptList,
          allData: receiptList,
          hasNextPage: receiptList.length === config.limit,
          totalCount,
          displayTensu,
        }));
      } catch (error) {
        logger({ error, message: "failed to fetch receipt list first page" });
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: "レセプト一覧の取得に失敗しました",
        }));

        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "レセプト一覧の取得に失敗しました",
          });
        }
      }
    },
    [createSearchInput, getReceiptList, config.limit, handleError],
  );

  // Load more data for infinite scroll
  const loadMore = useCallback(async () => {
    if (state.isLoadingMore || !state.hasNextPage) {
      return;
    }

    setState((prev) => ({
      ...prev,
      isLoadingMore: true,
      error: null,
    }));

    try {
      const queryInput = createSearchInput(false);

      const response = await getReceiptList({
        variables: { input: queryInput },
      });

      const newReceiptList =
        response.data?.postApiReceiptGetList?.data?.receiptList ?? [];
      const totalCount =
        response.data?.postApiReceiptGetList?.data?.totalCount ?? 0;
      const displayTensu =
        response.data?.postApiReceiptGetList?.data?.displayTensu ?? 0;

      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
        currentPage: newReceiptList,
        allData: [...prev.allData, ...newReceiptList],
        hasNextPage: newReceiptList.length === config.limit,
        totalCount,
        displayTensu,
      }));
    } catch (error) {
      logger({ error, message: "failed to fetch more receipt list" });
      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
        error: "追加データの取得に失敗しました",
      }));

      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "追加データの取得に失敗しました",
        });
      }
    }
  }, [
    createSearchInput,
    getReceiptList,
    config.limit,
    state.isLoadingMore,
    state.hasNextPage,
    handleError,
  ]);

  const updateSort = useCallback((sortKey: number, sortOrder: boolean) => {
    setConfig((prev) => ({
      ...prev,
      sort: { sortKey, sortOrder },
    }));
  }, []);

  const updateFilter = useCallback((filterType: number) => {
    setConfig((prev) => ({
      ...prev,
      filter: { filterType },
    }));
  }, []);

  const updateFilterAndReload = useCallback(
    async (filterType: number) => {
      setState({
        currentPage: [],
        allData: [],
        isLoading: false,
        isLoadingMore: false,
        hasNextPage: true,
        error: null,
        totalCount: 0,
        displayTensu: 0,
      });

      // Update config
      setConfig((prev) => ({
        ...prev,
        filter: { filterType },
      }));

      // Create search input with new filter
      const paginationParams = {
        limit: config.limit,
        filterType: filterType, // Use the new filterType directly
        sortKey: config.sort.sortKey,
        sortOrder: config.sort.sortOrder,
      };

      const queryInput = convertFormInputToSearchInput({
        ...currentSearchCriteria,
        ...paginationParams,
      });

      setState((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      try {
        const response = await getReceiptList({
          variables: { input: queryInput },
        });

        const receiptList =
          response.data?.postApiReceiptGetList?.data?.receiptList ?? [];
        const totalCount =
          response.data?.postApiReceiptGetList?.data?.totalCount ?? 0;
        const displayTensu =
          response.data?.postApiReceiptGetList?.data?.displayTensu ?? 0;

        console.log("📥 updateFilterAndReload response:", {
          receiptListLength: receiptList.length,
          totalCount,
          displayTensu,
          limit: config.limit,
          hasNextPage: receiptList.length === config.limit,
        });

        setState((prev) => ({
          ...prev,
          isLoading: false,
          currentPage: receiptList,
          allData: receiptList,
          hasNextPage: receiptList.length === config.limit,
          totalCount,
          displayTensu,
        }));
      } catch (error) {
        logger({
          error,
          message: "failed to fetch receipt list with new filter",
        });
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: "レセプト一覧の取得に失敗しました",
        }));

        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "レセプト一覧の取得に失敗しました",
          });
        }
      }
    },
    [
      currentSearchCriteria,
      config.limit,
      config.sort.sortKey,
      config.sort.sortOrder,
      getReceiptList,
      handleError,
    ],
  );

  const updateSearchCriteria = useCallback(
    (newCriteria: SearchCriteriaFormType) => {
      console.log("📊 updateSearchCriteria called with:", {
        seikyuYm: newCriteria.seikyuYm?.format("YYYY/MM"),
        oldSeikyuYm: currentSearchCriteria.seikyuYm?.format("YYYY/MM"),
        criteriaKeys: Object.keys(newCriteria),
      });

      setCurrentSearchCriteria(newCriteria);
      setState({
        currentPage: [],
        allData: [],
        isLoading: false,
        isLoadingMore: false,
        hasNextPage: true,
        error: null,
        totalCount: 0,
        displayTensu: 0,
      });

      console.log("📊 Search criteria updated successfully");
    },
    [currentSearchCriteria],
  );

  const reset = useCallback(() => {
    setState({
      currentPage: [],
      allData: [],
      isLoading: false,
      isLoadingMore: false,
      hasNextPage: true,
      error: null,
      totalCount: 0,
      displayTensu: 0,
    });
  }, []);

  const returnValue = useMemo(
    () => ({
      ...state,
      config,
      loadFirstPage,
      loadMore,
      updateSort,
      updateFilter,
      updateFilterAndReload,
      updateSearchCriteria,
      reset,
      isEmpty: state.allData.length === 0 && !state.isLoading,
    }),
    [
      state,
      config,
      loadFirstPage,
      loadMore,
      updateSort,
      updateFilter,
      updateFilterAndReload,
      updateSearchCriteria,
      reset,
    ],
  );

  return returnValue;
};

export type UseReceiptPaginationReturn = ReturnType<
  typeof useReceiptPagination
>;
