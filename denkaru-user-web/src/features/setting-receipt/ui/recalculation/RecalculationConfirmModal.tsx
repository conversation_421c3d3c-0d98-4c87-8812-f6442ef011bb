import { Progress } from "antd";
import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { generateProgressMessage } from "@/features/setting-receipt/utils/recalculation";

import { useModal } from "../../hooks/useReceiptListModalProviders";

import type { FC } from "react";

const Wrapper = styled.div`
  padding: 20px;
`;

const WrapperProcess = styled.div`
  padding: 0px 20px;
`;

const LoadingWrapper = styled.div`
  padding: 80px 0px;
  flex-direction: column;
  display: flex;
  justify-content: center;
`;

type Props = {
  onExec: () => void;
  onCancelRequest: () => void;
  isLoading: boolean;
  total: number;
  totalProcess: number;
  type: number;
};

export const RecalculationConfirmModal: FC<Props> = ({
  onExec,
  onCancelRequest,
  isLoading,
  total,
  totalProcess,
  type,
}) => {
  const { modal, handleCloseModal } = useModal();
  const percent = total > 0 ? (totalProcess / total) * 100 : 0;
  const message = generateProgressMessage(type, total, totalProcess);
  return (
    <Modal
      title="進捗状況"
      isOpen={modal.recalculationConfirmOpen}
      destroyOnClose
      centerFooterContent={isLoading}
      width={400}
      footer={
        isLoading
          ? [
              <Button
                key="request-cancel"
                varient="tertiary"
                onClick={onCancelRequest}
              >
                キャンセル
              </Button>,
            ]
          : [
              <Button
                varient="tertiary"
                key="cancel"
                onClick={() => handleCloseModal("RECALCULATION_CONFIRM")}
              >
                キャンセル
              </Button>,
              <Button varient="primary" key="exec" onClick={onExec}>
                実行
              </Button>,
            ]
      }
    >
      <WrapperProcess>
        {isLoading ? (
          <LoadingWrapper>
            <p style={{ marginBottom: 8 }}>
              {message || "処理を実行しています"}
            </p>
            <Progress
              strokeColor={"#43c3d5"}
              percent={percent}
              showInfo={false}
            />
          </LoadingWrapper>
        ) : (
          <Wrapper>
            <p>処理を実行しますか？</p>
          </Wrapper>
        )}
      </WrapperProcess>
    </Modal>
  );
};
