import { useCallback, useEffect, useRef, useState } from "react";

import axios from "axios";
import dayjs from "dayjs";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";
import { DatePicker } from "@/components/ui/DatePicker";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { logger } from "@/utils/sentry-logger";

import { useModal } from "../../hooks/useReceiptListModalProviders";
import { useReceiptSearch } from "../../hooks/useReceiptListSearchProvider";
import { replaceNewlinesWithBr } from "../../utils";
import {
  convertLastRecalculationStreamToJson,
  convertRecalculationStreamToJson,
} from "../../utils/recalculation";
import { recalculation } from "../../utils/rest";

import { CheckOptionModal } from "./CheckOptionModal";
import { PatientSearchTableForm } from "./PatientSearchTableForm";
import { RecalculationConfirmModal } from "./RecalculationConfirmModal";

import type { Dayjs } from "dayjs";
import type { CancelTokenSource } from "axios";
import type { RecalculationFormType } from "../../types/recalculation";

const StyledModal = styled(Modal)`
  .ant-modal-body {
    overflow: hidden;
    overflow-y: auto;
    max-height: 600px;
  }

  .ant-modal-footer {
    .ant-btn {
      &:first-of-type {
        margin-right: auto;
      }
    }
  }
`;

const MainWrapper = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e3e5;
`;

const InputSection = styled.div`
  &:not(:last-of-type) {
    margin-bottom: 20px;
  }
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const CheckboxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StyledButton = styled(Button)`
  width: 140px;
  height: 28px;
`;

const TextWithEscape = styled.p`
  white-space: pre-line;
`;

type Props = {
  sinYm?: Dayjs | undefined;
  isProcess?: boolean;
};

export const RecalculationModal = ({ sinYm, isProcess = false }: Props) => {
  const [isCheckOptionOpen, setIsCheckOptionOpen] = useState(false);

  const { modal, handleOpenModal, handleCloseModal, handleCloseAllModal } =
    useModal();

  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();

  const { getValues: getReceiptSearchValues, search } = useReceiptSearch();

  const { seikyuYm } = getReceiptSearchValues();

  const { control, getValues } = useForm<RecalculationFormType>({
    defaultValues: {
      sinYm: sinYm ?? seikyuYm ?? dayjs(),
      ptlist: [],
      isCheckErrorCheckBox: true,
      isReceiptAggregationCheckBox: true,
      isRecalculationCheckBox: false,
    },
  });

  const [isProgressing, setIsProgressing] = useState(false);
  const [processCalculation, setProcessCalculation] = useState({
    totalRecord: 0,
    totalProcess: 0,
    type: 0,
  });
  const cancelTokenSource = useRef<CancelTokenSource>(
    axios.CancelToken.source(),
  );

  useEffect(() => {
    if (isProcess) {
      setIsProgressing(true);
    }
  }, [isProcess]);

  const handleRequestCancel = useCallback(() => {
    if (isProcess) {
      handleCloseModal("RECALCULATION_CONFIRM");
      setIsProgressing(false);
      return;
    }

    cancelTokenSource.current.cancel("cancel");
    setProcessCalculation({
      totalRecord: 0,
      totalProcess: 0,
      type: 0,
    });
  }, []);

  const handleRecalculation = async () => {
    try {
      const {
        sinYm,
        ptlist,
        isCheckErrorCheckBox,
        isReceiptAggregationCheckBox,
        isRecalculationCheckBox,
      } = getValues();

      if (!sinYm) {
        notification.error({ message: "処理年月を設定してください。" });
        return;
      }

      if (
        !isCheckErrorCheckBox &&
        !isReceiptAggregationCheckBox &&
        !isRecalculationCheckBox
      ) {
        notification.error({ message: "処理選択を設定してください。" });
        return;
      }

      const targetIdList = (ptlist ?? [])
        .map((item) => item.ptId)
        .filter((id) => id !== "");

      setIsProgressing(true);

      const response = await recalculation(
        {
          sinYm: Number(sinYm.format("YYYYMM")),
          ptIdList: targetIdList,
          isCheckErrorCheckBox,
          isReceiptAggregationCheckBox,
          isRecalculationCheckBox,
        },
        {
          responseType: "stream",
          cancelToken: cancelTokenSource.current.token,
          onDownloadProgress: (progressEvent) => {
            const resText = progressEvent.event.currentTarget.responseText;
            const listTextLine = resText.split("\n");
            const endTextLine = listTextLine[listTextLine.length - 1];
            const msgJson = convertRecalculationStreamToJson(endTextLine);
            setProcessCalculation((prevState) => {
              return {
                ...prevState,
                type: msgJson.type,
                totalProcess: msgJson.successCount,
                totalRecord: msgJson.length,
              };
            });
          },
        },
      );

      const last = convertLastRecalculationStreamToJson(response.data);

      if (!last.message.length) {
        handleCloseAllModal();
        notification.success({ message: "処理が完了しました。" });
      } else {
        handleCloseAllModal();
        notification.error({
          message: (
            <TextWithEscape>
              {replaceNewlinesWithBr(last.message)}
            </TextWithEscape>
          ),
        });
      }

      await search(getReceiptSearchValues());
    } catch (error) {
      if (axios.isCancel(error)) {
        cancelTokenSource.current = axios.CancelToken.source();
        notification.info({
          message: "リクエストをキャンセルしました。",
        });
        return;
      }

      logger({ error, message: "failed to execute recalculation" });
      if (error instanceof Error) {
        handleError({ error, commonMessage: "処理に失敗しました。" });
      }
    } finally {
      setIsProgressing(false);
    }
  };

  return (
    <>
      <StyledModal
        width={480}
        title="集計(再計算)"
        isOpen={modal.recalculationOpen}
        footer={[
          <Button
            key="cancel"
            varient="tertiary"
            onClick={() => handleCloseModal("RECALCULATION")}
          >
            キャンセル
          </Button>,
          <Button
            key="exec"
            varient="primary"
            onClick={() => handleOpenModal("RECALCULATION_CONFIRM")}
          >
            実行
          </Button>,
        ]}
      >
        <MainWrapper>
          <InputSection>
            <p>
              診療情報および、レセプト情報を再作成します。
              <br />
              この操作にはしばらく時間がかかる可能性があります。
            </p>
          </InputSection>

          <InputSection>
            <StyledLabel label="処理年月" />
            <Controller
              name="sinYm"
              control={control}
              render={({ field }) => (
                <DatePicker
                  {...field}
                  picker="month"
                  format="YYYY/MM"
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                />
              )}
            />
          </InputSection>

          <InputSection>
            <StyledLabel label="処理選択" />
            <CheckboxWrapper>
              <Controller
                name="isRecalculationCheckBox"
                control={control}
                render={({ field }) => (
                  <Checkbox {...field} checked={field.value}>
                    再計算
                  </Checkbox>
                )}
              />
              <Controller
                name="isReceiptAggregationCheckBox"
                control={control}
                render={({ field }) => (
                  <Checkbox {...field} checked={field.value}>
                    レセプト集計
                  </Checkbox>
                )}
              />
              <div>
                <Controller
                  name="isCheckErrorCheckBox"
                  control={control}
                  render={({ field }) => (
                    <Checkbox {...field} checked={field.value}>
                      レセプトチェック
                    </Checkbox>
                  )}
                />
                <StyledButton
                  varient="standard-sr"
                  onClick={() => setIsCheckOptionOpen(true)}
                >
                  チェックオプション
                </StyledButton>
              </div>
            </CheckboxWrapper>
          </InputSection>
        </MainWrapper>

        <PatientSearchTableForm control={control} />
      </StyledModal>

      <CheckOptionModal
        isOpen={isCheckOptionOpen}
        onClose={() => setIsCheckOptionOpen(false)}
      />
      <RecalculationConfirmModal
        onExec={() => handleRecalculation()}
        onCancelRequest={handleRequestCancel}
        isLoading={isProgressing}
        total={processCalculation.totalRecord}
        totalProcess={processCalculation.totalProcess}
        type={processCalculation.type}
      />
    </>
  );
};
