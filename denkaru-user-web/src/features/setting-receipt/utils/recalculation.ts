import type { GetApiReceiptGetReceCheckOptionListQuery } from "@/apis/gql/operations/__generated__/receipt";
import type {
  RecalculationStreamProgressResponse,
  ReceCheckOptionFormType,
  ReceCheckOptionInput,
} from "../types/recalculation";

type ReceCheckOption = NonNullable<
  NonNullable<
    NonNullable<
      GetApiReceiptGetReceCheckOptionListQuery["getApiReceiptGetReceCheckOptionList"]
    >["data"]
  >["receCheckOptionList"]
>[number];

const pickOptionValue = (
  checkboxKey: string,
  receCheckOptionList: ReceCheckOption[],
): ReceCheckOptionInput => {
  const target = receCheckOptionList.find((option) => {
    return option.checkboxKey === checkboxKey;
  });

  return target
    ? {
        checkboxKey,
        errCd: target.errCd,
        isInvalid: target.isInvalid,
        checkOpt: target.checkOpt,
      }
    : {
        checkboxKey,
      };
};

export const formatReceCheckFormValues = (
  receCheckOptionList: ReceCheckOption[],
): ReceCheckOptionFormType => {
  return {
    hokenYukoKigenChekku: pickOptionValue(
      "HokenYukoKigenChekku",
      receCheckOptionList,
    ),
    hokenMiKakuninChekku: pickOptionValue(
      "HokenMiKakuninChekku",
      receCheckOptionList,
    ),
    byomeiMiNyuryokuChekku: pickOptionValue(
      "ByomeiMiNyuryokuChekku",
      receCheckOptionList,
    ),
    byomeiShoshinryoToByomeiChekku: pickOptionValue(
      "ByomeiShoshinryoToByomeiChekku",
      receCheckOptionList,
    ),
    byomeiShuByomeiChekku: pickOptionValue(
      "ByomeiShuByomeiChekku",
      receCheckOptionList,
    ),
    byomeiHaishiByomeiChekku: pickOptionValue(
      "ByomeiHaishiByomeiChekku",
      receCheckOptionList,
    ),
    byomeiMiKodoKaByomeiChekku: pickOptionValue(
      "ByomeiMiKodoKaByomeiChekku",
      receCheckOptionList,
    ),
    byomeiUtagaiByomeiChekku: pickOptionValue(
      "ByomeiUtagaiByomeiChekku",
      receCheckOptionList,
    ),
    byomeiTekioByomeiChekku: pickOptionValue(
      "ByomeiTekioByomeiChekku",
      receCheckOptionList,
    ),
    byomeiBuiOrderByomeiChekku: pickOptionValue(
      "ByomeiBuiOrderByomeiChekku",
      receCheckOptionList,
    ),
    byomeiBuiMedicalByomeiChekku: pickOptionValue(
      "ByomeiBuiMedicalByomeiChekku",
      receCheckOptionList,
    ),
    duplicatedByomeiCheckku: pickOptionValue(
      "DuplicatedByomeiCheckku",
      receCheckOptionList,
    ),
    syusyokuByomeiCheckku: pickOptionValue(
      "SyusyokuByomeiCheckku",
      receCheckOptionList,
    ),
    shinryoYukoKigenChekku: pickOptionValue(
      "ShinryoYukoKigenChekku",
      receCheckOptionList,
    ),
    shinryoShoshinryoChekku: pickOptionValue(
      "ShinryoShoshinryoChekku",
      receCheckOptionList,
    ),
    shinryoSanteiKaisuChekku: pickOptionValue(
      "ShinryoSanteiKaisuChekku",
      receCheckOptionList,
    ),
    shinryoMiKodoKaTokuZaiChekku: pickOptionValue(
      "ShinryoMiKodoKaTokuZaiChekku",
      receCheckOptionList,
    ),
    shinryoNenreiChekku: pickOptionValue(
      "ShinryoNenreiChekku",
      receCheckOptionList,
    ),
    komentoChekku: pickOptionValue("KomentoChekku", receCheckOptionList),
    additionItemChekku: pickOptionValue(
      "AdditionItemChekku",
      receCheckOptionList,
    ),
    shohoToyoNissuChekku: pickOptionValue(
      "ShohoToyoNissuChekku",
      receCheckOptionList,
    ),
    shohoDoshuDoKoChekku: pickOptionValue(
      "ShohoDoshuDoKoChekku",
      receCheckOptionList,
    ),
    // TODO: 要確認
    // isCheckListPrinting: pickOptionValue(
    //   "IsCheckListPrinting",
    //   receCheckOptionList,
    // ),
  };
};

export const generateProgressMessage = (
  type: number,
  totalRecord: number,
  totalProcess: number,
): string => {
  switch (type) {
    case 1:
      return `計算処理中 (${totalProcess.toLocaleString()} / ${totalRecord.toLocaleString()})`;
    case 2:
      return `レセ集計中 (${totalProcess.toLocaleString()} / ${totalRecord.toLocaleString()})`;
    case 3:
      return `レセチェック処理中 (${totalProcess.toLocaleString()} / ${totalRecord.toLocaleString()})`;
    default:
      return "";
  }
};

export const convertRecalculationStreamToJson = (inputString: string) => {
  try {
    return JSON.parse(inputString) as RecalculationStreamProgressResponse;
  } catch (error) {
    throw new Error("failed to parse recalculation stream data to json");
  }
};

export const convertLastRecalculationStreamToJson = (inputString: string) => {
  try {
    return JSON.parse(
      inputString.split(/\r?\n/)?.at(-1) ?? "{}",
    ) as RecalculationStreamProgressResponse;
  } catch (error) {
    throw new Error("failed to parse last recalculation stream data to json");
  }
};
