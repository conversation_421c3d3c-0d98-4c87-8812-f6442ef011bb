import type dayjs from "dayjs";
import type { SortOrder } from "@/apis/gql/generated/types";
import type { valueOf } from "@/types/utility-types";
import type {
  CsvStatus,
  CsvStatusInput,
  DesiredDateStatus,
  DesiredType,
  GuidanceStatus,
  GuidanceStatusInput,
  PaymentStatus,
  PaymentStatusInput,
  PostalService,
  ShowFutureTreatment,
  SmsStatus,
  Status,
  StatusInput,
  VideocallStatus,
} from "../constants/table";

/** 服薬指導希望日時ステータス */
export type DesiredDateStatusType = valueOf<typeof DesiredDateStatus>;

/** 服薬指導希望日時タイプ */
export type DesiredTypeType = valueOf<typeof DesiredType>;

/** ステータス */
export type StatusType = valueOf<typeof Status>;

/** 服薬指導ステータス */
export type GuidanceStatusType = valueOf<typeof GuidanceStatus>;

/** 会計ステータス */
export type PaymentStatusType = valueOf<typeof PaymentStatus>;

/** CSVステータス */
export type CsvStatusType = valueOf<typeof CsvStatus>;

/** SMSステータス */
export type SmsStatusType = valueOf<typeof SmsStatus>;

/** ビデオ通話ステータス */
export type VideocallStatusType = valueOf<typeof VideocallStatus>;

/** 郵送サービス種別 */
export type PostalServiceType = valueOf<typeof PostalService>;

/** ステータス検索パラメータ */
export type StatusInputType = valueOf<typeof StatusInput>;

/** 服薬指導ステータス検索パラメータ */
export type GuidanceStatusInputType = valueOf<typeof GuidanceStatusInput>;

/** 会計ステータス検索パラメータ */
export type PaymentStatusInputType = valueOf<typeof PaymentStatusInput>;

/** CSVステータス検索パラメータ */
export type CsvStatusInputType = valueOf<typeof CsvStatusInput>;

/** 未来日表示フラグ */
export type ShowFutureTreatmentType = valueOf<typeof ShowFutureTreatment>;

/** 受付一覧 検索パラメータ */
export type SearchType = {
  displayDate: dayjs.Dayjs;
  pharmacyDesiredDate: SortOrder | undefined;
  pharmacyReserveUpdateDate: SortOrder | undefined;
  reserveTime: SortOrder | undefined;
  status: StatusInputType;
  guidanceStatus: GuidanceStatusInputType;
  showFutureTreatment: ShowFutureTreatmentType;
  paymentStatus: PaymentStatusInputType;
  csvStatus: CsvStatusInputType;
  customer: string | undefined;
  patient: string | undefined;
  clinic: string | undefined;
};

export type ReceptionTableDataType = {
  /** 予約に紐づく患者ごとのインデックス（1から採番） */
  index: number;

  /** 薬局予約ID */
  pharmacyReserveId: number;

  /** 薬局予約詳細ID */
  pharmacyReserveDetailId: number;

  /** 診療予約ID */
  reserveId: number | undefined;

  /** 予約に紐づく患者（pharmacyReserveDetails）の数 */
  groupCount: number;

  /** 服薬指導希望日時 */
  desiredDateStatus: DesiredDateStatusType;

  /** 希望日時　*/
  desiredDate: string | undefined;

  /** 希望日時タイプ */
  desiredType: DesiredTypeType | undefined;

  /** 服薬指導更新日時 */
  reserveUpdateDate: string;

  /** クリニック名 */
  clinicName: string;

  /** 診療予約開始日時 */
  reserveStartDate: string;

  /** 診療予約終了日時 */
  reserveEndDate: string;

  /** テーブルのインデックス */
  key: number;

  /** 患者ID */
  patientId: number;

  /** 患者氏名 */
  name: string;

  /** 患者氏名（カナ） */
  kanaName: string;

  /** 性別 */
  sex: number;

  /** 生年月日 */
  birthday: string;

  /** ステータス */
  status: StatusType;

  /** 服薬指導ステータス */
  guidanceStatus: GuidanceStatusType;

  /** SMSステータス */
  smsStatus: SmsStatusType;

  /** ビデオ通話ステータス */
  videocallStatus: VideocallStatusType;

  /** ビデオ通話ID */
  meetingId: number | undefined;

  /** 会計ステータス */
  paymentStatus: PaymentStatusType;

  /** 郵送サービス種別 */
  postalServiceType: PostalServiceType;

  /** CSVステータス */
  csvStatus: CsvStatusType;

  /** 予約に紐づく患者の服薬指導ステータスがすべて「指導済」かどうか */
  isAllGuidanceFinished: boolean;

  /** 予約に紐づく患者のステータスがすべて「発送済」かどうか */
  isReceptionComplete: boolean;

  /** 予約に紐づく患者のステータスがすべて「キャンセル」かどうか */
  isReceptionCancelled: boolean;

  /**
   * すべての会計処理が完了しているかどうか
   * （予約に紐づく患者のステータスが「会計済」または「キャンセル」かどうか）
   *  */
  isAllPaymentActionDone: boolean;

  /** 予約に紐づく全ての患者名 */
  patientNameList: string[];

  /** 服薬指導単独予約かどうか */
  isOnlyPharmacyReservation: boolean;

  /** 処方箋の利用フラッグ */
  usesElectronicPrescription: boolean | undefined;

  /** 処方箋の控えの持ちフラッグ */
  hasElectronicPrescription: boolean | undefined;

  /** 引換番号 */
  redemptionNumber: string | undefined;

  /** メモ */
  memo: string;
};
