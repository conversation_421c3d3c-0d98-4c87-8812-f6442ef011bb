import dayjs from "dayjs";

import {
  DesiredDateStatus,
  DesiredType,
  GuidanceStatus,
  PaymentStatus,
  Status,
} from "../constants/table";

import type { CSSProperties } from "react";
import type { FindPharmacyReservesQuery } from "@/apis/gql/operations/__generated__/pharmacy-reserve";
import type {
  CsvStatusType,
  DesiredDateStatusType,
  DesiredTypeType,
  GuidanceStatusType,
  PaymentStatusType,
  PostalServiceType,
  ReceptionTableDataType,
  SmsStatusType,
  StatusType,
  VideocallStatusType,
} from "../types/table";

const convertToMappedType = <T>(value: number): T => {
  const numberStr = value.toString();

  // FIXME: 型の整合性チェックする

  return numberStr as T;
};

export const generateTableDataSource = (
  receptions: NonNullable<FindPharmacyReservesQuery["findPharmacyReserves"]>,
): ReceptionTableDataType[] => {
  return receptions
    .map(
      ({
        pharmacyReserveId,
        pharmacyReserveDetails,
        desiredDateStatus,
        desiredDate,
        reserve,
        smsStatus,
        videocallStatus,
        postalServiceType,
        csvStatus,
        reserveUpdateDate,
        meeting,
        memo,
      }) => {
        return pharmacyReserveDetails.map(
          (
            {
              pharmacyReserveDetailId,
              patient,
              customer,
              status,
              guidanceStatus,
              paymentStatus,
              usesElectronicPrescription,
              hasElectronicPrescription,
              redemptionNumber,
            },
            index,
          ) => ({
            index: index + 1,
            pharmacyReserveId,
            pharmacyReserveDetailId,
            reserveId: reserve?.reserveId,
            groupCount: pharmacyReserveDetails.length,
            desiredDateStatus:
              convertToMappedType<DesiredDateStatusType>(desiredDateStatus),
            desiredDate: desiredDate?.[0]?.desiredDate ?? undefined,
            desiredType: desiredDate?.[0]?.desiredType
              ? convertToMappedType<DesiredTypeType>(desiredDate[0].desiredType)
              : undefined,
            reserveUpdateDate,
            clinicName: reserve?.clinicName ?? "",
            reserveStartDate: reserve?.reserveStartDate ?? "",
            reserveEndDate: reserve?.reserveEndDate ?? "",
            patientId: patient.ptId,
            name: customer.name ?? "",
            kanaName: customer.kanaName ?? "",
            sex: customer.gender ?? patient.sex,
            birthday: customer.birthday ?? patient.birthday,
            status: convertToMappedType<StatusType>(status),
            guidanceStatus:
              convertToMappedType<GuidanceStatusType>(guidanceStatus),
            smsStatus: convertToMappedType<SmsStatusType>(smsStatus),
            videocallStatus:
              convertToMappedType<VideocallStatusType>(videocallStatus),
            meetingId: meeting?.meetingId,
            paymentStatus:
              convertToMappedType<PaymentStatusType>(paymentStatus),
            postalServiceType:
              convertToMappedType<PostalServiceType>(postalServiceType),
            csvStatus: convertToMappedType<CsvStatusType>(csvStatus),
            isReceptionComplete: pharmacyReserveDetails.every(
              (detail) => detail.status === Number(Status.Sent),
            ),
            isReceptionCancelled: pharmacyReserveDetails.every(
              (detail) => detail.status === Number(Status.Cancelled),
            ),
            patientNameList: pharmacyReserveDetails.map(
              (detail) => detail.customer.name ?? "",
            ),
            isAllPaymentActionDone: pharmacyReserveDetails.every(
              (detail) =>
                detail.paymentStatus === Number(PaymentStatus.PaymentDone) ||
                detail.status === Number(Status.Cancelled),
            ),
            isAllGuidanceFinished: pharmacyReserveDetails.every(
              (detail) =>
                detail.guidanceStatus ===
                Number(GuidanceStatus.GuidanceFinished),
            ),
            isOnlyPharmacyReservation: !reserve,
            usesElectronicPrescription,
            hasElectronicPrescription,
            redemptionNumber,
            memo: memo ?? "",
          }),
        );
      },
    )
    .flatMap((item) => item)
    .map((flatItem, index) => ({
      ...flatItem,
      key: index,
    }));
};

export const getTableRowSpanByReserveGroup = (
  record: ReceptionTableDataType,
): number => {
  return record.groupCount === 1
    ? 1
    : record.index === 1 // 先頭からグループ数分を結合する
      ? record.groupCount
      : 0;
};

export const getDesiredDateColumnBackgroundColor = (
  record: ReceptionTableDataType,
): CSSProperties | undefined => {
  if (record.desiredDateStatus !== DesiredDateStatus.Set) {
    // 希望日時が設定されていない場合は不要
    return undefined;
  }
  if (record.desiredType === DesiredType.Unspecified) {
    // 時間指定がない場合は不要
    return undefined;
  }
  if (record.isReceptionCancelled) {
    // 受付が全てキャンセル済の場合は不要
    return undefined;
  }
  if (record.isAllGuidanceFinished) {
    // 全ての患者が服薬指導済の場合は不要
    return undefined;
  }
  if (
    typeof record.desiredDate === "undefined" ||
    !dayjs(record.desiredDate).isValid()
  ) {
    // 不正なデータの場合（型チェック）
    return undefined;
  }

  if (record.desiredType === DesiredType.Morning) {
    const isWithinTime = dayjs().isBetween(
      dayjs(record.desiredDate).hour(10).minute(0).second(0),
      dayjs(record.desiredDate).hour(11).minute(59).second(59),
      "minute",
      "[]",
    );

    // 期間内
    if (isWithinTime) {
      return { backgroundColor: "#bbf4fccc" };
    }

    const isExpiredTime = dayjs().isAfter(
      dayjs(record.desiredDate).hour(12).minute(0).second(0),
    );

    // 時間超過
    if (isExpiredTime) {
      return { backgroundColor: "#FF13131F" };
    }
  }

  if (record.desiredType === DesiredType.Specified) {
    const isWithinTime = dayjs().isBetween(
      dayjs(record.desiredDate),
      dayjs(record.desiredDate).add(1, "h"),
      "minute",
      "[]",
    );

    // 期間内
    if (isWithinTime) {
      return { backgroundColor: "#bbf4fccc" };
    }

    const isExpiredTime = dayjs().isAfter(record.desiredDate);

    // 時間超過
    if (isExpiredTime) {
      return { backgroundColor: "#FF13131F" };
    }
  }

  return undefined;
};

export const getReserveTimeColumnBackgroundColor = (
  record: ReceptionTableDataType,
): CSSProperties | undefined => {
  if (record.isOnlyPharmacyReservation || !record.reserveStartDate) {
    return undefined;
  }

  if (record.status === Status.PrescriptionUnarrived) {
    const now = dayjs();

    const isTodayReservationStarted =
      now.isSame(dayjs(record.reserveStartDate), "day") &&
      now.isBefore(dayjs(record.reserveEndDate));

    // 当日の予約が開始されている場合：青
    if (isTodayReservationStarted) {
      return { backgroundColor: "#bbf4fccc" };
    }

    const isAfterEndTime = now.isAfter(dayjs(record.reserveEndDate));

    // 予約終了日時以降の場合：赤
    if (isAfterEndTime) {
      return { backgroundColor: "#FF13131F" };
    }
  }

  return undefined;
};
