import { Flex } from "antd";
import styled, { css } from "styled-components";

import { calcAge } from "@/utils/date-helper";

import { useDrawer } from "../../providers/DrawerProvider";
import { useReceptionSelect } from "../../providers/ReceptionSelectProvider";

import type { ReceptionTableDataType } from "../../types/table";

const Name = styled.button`
  color: #007aff;
  background-color: transparent;
  border: none;
  display: block;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
  text-align: left;
`;

const Furigana = styled.span`
  font-size: 12px;
`;

const Age = styled.span`
  min-width: fit-content;
  line-height: normal;
`;

const GenderTag = styled.span<{ $sex: number }>`
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 12px;
  border-radius: 2px;
  color: #fff;
  font-weight: bold;
  text-align: center;

  ${({ $sex }) => {
    if ($sex === 1) {
      return css`
        background-color: #006ec3;
      `;
    }

    if ($sex === 2) {
      return css`
        background-color: #f48e91;
      `;
    }

    return null;
  }}
`;

type Props = {
  record: ReceptionTableDataType;
};

export const TablePatientColumn: React.FC<Props> = ({ record }) => {
  const { name, kanaName, birthday, sex } = record;
  const { handleOpenDrawer, handleChangeActiveTab } = useDrawer();
  const { handleSelectReception } = useReceptionSelect();

  const { year: age } = calcAge(birthday);

  return (
    <>
      <Furigana>{kanaName}</Furigana>
      <Flex gap={4} align="flex-start">
        <Name
          onClick={() => {
            handleSelectReception({ reception: record });
            handleOpenDrawer();
            handleChangeActiveTab("2");
          }}
        >
          {name}
        </Name>
        <GenderTag $sex={sex}>
          {sex === 1 ? "M" : sex === 2 ? "F" : "　"}
        </GenderTag>
        <Age>{age}</Age>
      </Flex>
    </>
  );
};
