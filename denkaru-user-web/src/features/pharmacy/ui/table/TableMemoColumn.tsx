import { useEffect, useState } from "react";

import styled from "styled-components";

import { TextAreaInput } from "@/components/ui/TextAreaInput";
import { useDebounce } from "@/hooks/useDebounce";
import { useErrorHandler } from "@/hooks/useErrorHandler";
// import { useUpdatePharmacyReserveMemoMutation } from "@/apis/gql/operations/__generated__/pharmacy-reserve";

// import type { UpdatePharmacyReserveMemoInput } from "@/apis/gql/generated/types";
import type { ReceptionTableDataType } from "../../types/table";

type Props = {
  record: ReceptionTableDataType;
  rowSpan: number;
};

export const TableMemoColumn: React.FC<Props> = ({ record, rowSpan }) => {
  const [inputText, setInputText] = useState<string | undefined>(record.memo);
  const [lastSavedMemo, setLastSavedMemo] = useState<string>(record.memo);

  useEffect(() => {
    setInputText(record.memo);
    setLastSavedMemo(record.memo);
  }, [record.memo]);

  const debouncedInput = useDebounce(inputText);

  // const [saveMemo] = useUpdatePharmacyReserveMemoMutation();
  const { handleError } = useErrorHandler();

  const handleInputText = (input: string) => {
    setInputText(input);
  };

  //Make sure saveMemo is not triggered excessively on reception list change
  // useEffect(() => {
  //   if (
  //     typeof debouncedInput === "undefined" ||
  //     debouncedInput === lastSavedMemo
  //   ) {
  //     return;
  //   }
  //   const input: UpdatePharmacyReserveMemoInput = {
  //     pharmacyReserveId: record.pharmacyReserveId,
  //     memo: debouncedInput,
  //   };
  //   saveMemo({
  //     variables: { input },
  //     onError: (error) => {
  //       handleError({ error });
  //     },
  //     onCompleted: () => {
  //       setLastSavedMemo(debouncedInput);
  //     },
  //   });

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [debouncedInput]);

  return (
    <StyledTextAreaInput
      rows={20}
      rowSpan={rowSpan}
      maxLength={200}
      value={inputText}
      onChange={(e) => {
        handleInputText(e.target.value);
      }}
    />
  );
};

const StyledTextAreaInput = styled(TextAreaInput)<{ rowSpan: number }>`
  resize: none !important;
  max-height: ${({ rowSpan }) => rowSpan! * 68}px !important;
  border: 1px solid #89929a;
`;
