import { useRouter } from "next/router";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { PasswordInput } from "@/components/ui/PasswordInput";
import { TextInput } from "@/components/ui/TextInput";
import { NewPasswordRule } from "@/constants/validation";
import { useLoginInitial } from "@/hooks/useLoginInitial";
import { useSession } from "@/hooks/useSession";

const Wrapper = styled.div`
  padding: 20px 0 0;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  margin: 0 24px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;
`;

const StyledInput = styled(TextInput)`
  width: 432px;
  height: 36px;
`;

const StyledPasswordInput = styled(PasswordInput)`
  width: 432px;
  height: 36px;
`;

const InputNote = styled.div`
  font-size: 12px;
  color: #6a757d;
  margin-top: 4px;
`;

const ErrorText = styled(CommonErrorText)`
  padding-top: 4px;
`;

export const LoginInitialModal = () => {
  const router = useRouter();
  const redirectPath =
    router.isReady && typeof router.query.returnUrl === "string"
      ? router.query.returnUrl
      : "/start";
  const { control, submitting, onSubmit, errors } =
    useLoginInitial(redirectPath);

  const { session } = useSession();
  const { isLoginIdInitialized } = session;

  return (
    <Modal
      isOpen={true}
      title="ログインID・パスワードの設定"
      width={480}
      centered
      centerFooterContent
      footer={[
        <Button
          key="submit"
          htmlType="submit"
          form="login-initial-form"
          varient="primary"
          disabled={submitting}
        >
          設定
        </Button>,
      ]}
    >
      {submitting && <ModalLoading />}
      <Wrapper>
        {isLoginIdInitialized && (
          <>
            初回ログインのため、新しいログインIDとパスワードの初期設定が必要です。
          </>
        )}
        {!isLoginIdInitialized && (
          <>初回ログインのため、パスワードの初期設定が必要です。</>
        )}
        <Form id="login-initial-form" onSubmit={onSubmit}>
          {isLoginIdInitialized && (
            <InputWrapper>
              <InputLabel label="新しいログインID" />
              <Controller
                name="newLoginId"
                control={control}
                rules={{ required: "新しいログインIDを入力してください" }}
                render={({ field }) => (
                  <StyledInput {...field} hasError={!!errors.newLoginId} />
                )}
              />
              <InputNote>
                ※
                1文字以上30文字以内、半角英数字及び記号（「@」「.」「-」「_」）を使用できます。
              </InputNote>
              {errors.newLoginId && (
                <ErrorText>{errors.newLoginId.message}</ErrorText>
              )}
            </InputWrapper>
          )}
          <InputWrapper>
            <InputLabel label="新しいパスワード" />
            <Controller
              name="newPassword"
              control={control}
              rules={NewPasswordRule}
              render={({ field }) => (
                <StyledPasswordInput
                  {...field}
                  hasError={!!errors.newPassword}
                  autoComplete="new-password"
                />
              )}
            />
            <InputNote>
              ※
              8文字以上、大文字、小文字、数字、記号のうち2種類以上を含めてください。
            </InputNote>
            {errors.newPassword && (
              <ErrorText>{errors.newPassword.message}</ErrorText>
            )}
          </InputWrapper>
          <InputWrapper>
            <InputLabel label="新しいパスワード（再入力）" />
            <Controller
              name="confirmPassword"
              control={control}
              rules={{
                required: "新しいパスワード（確認）を入力してください",
              }}
              render={({ field }) => (
                <StyledPasswordInput
                  {...field}
                  hasError={!!errors.confirmPassword}
                  autoComplete="new-password"
                />
              )}
            />
            {errors.confirmPassword && (
              <ErrorText>{errors.confirmPassword.message}</ErrorText>
            )}
          </InputWrapper>
        </Form>
      </Wrapper>
    </Modal>
  );
};
