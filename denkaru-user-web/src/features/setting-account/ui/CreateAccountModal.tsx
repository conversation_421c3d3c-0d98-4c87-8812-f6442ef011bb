import { Checkbox, Flex } from "antd";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form as CustomForm } from "@/components/functional/Form";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { SvgIconTick } from "@/components/ui/Icon/IconTick";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { Segmented } from "@/components/ui/Segmented";
import { TextInput } from "@/components/ui/TextInput";
import { ManagerKbn, managerKbnList } from "@/constants/account";
import { FURIGANA_REGEXP } from "@/constants/validation";

import { adminPermissionList, permissionTypeList } from "../constants";
import { useCreateStaff } from "../hooks/useCreateStaff";

const Form = styled(CustomForm)`
  width: 100%;
  padding: 24px;
`;

const LoginIdCounter = styled.div`
  position: absolute;
  right: 24px;
  line-height: 1;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const LoginIdAttension = styled.p`
  font-size: 12px;
  line-height: 12px;
  padding-top: 4px;

  > span {
    color: #e74c3c;
  }
`;

const StyledTextInput = styled(TextInput)``;

const StyledHalfTextInput = styled(TextInput)`
  width: 320px;
`;

const StyledHalfHalfTextInput = styled(TextInput)`
  width: 160px;
`;

const ErrorText = styled(CommonErrorText)`
  padding-top: 4px;
`;

const ManagerKbnLabel = styled(InputLabel)`
  margin-bottom: 2px;
`;

const ManagerKbnText = styled.p<{ margin?: string }>`
  font-size: 14px;
  color: #6a757d;
  margin: ${(props) => props.margin};
`;

const List = styled.ul`
  display: flex;
  flex-wrap: wrap;
  width: 100%;
`;

const ListItem = styled.li`
  display: flex;
  align-items: center;
  width: 50%;
  height: 24px;
`;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (loginId: string, password: string) => void;
};

export const CreateAccountModal: React.FC<Props> = ({
  isOpen,
  onClose,
  onComplete,
}) => {
  const {
    submitting,
    onSubmit,
    handleClose,
    watch,
    errors,
    control,
    currentManagerKbn,
    resetPermissions,
  } = useCreateStaff(onComplete, onClose);

  const loginId = watch("loginId");

  return (
    <Modal
      isOpen={isOpen}
      onCancel={onClose}
      title="利用者の登録"
      width={760}
      centered
      footer={[
        <Button key="cancel" varient="tertiary" onClick={handleClose}>
          キャンセル
        </Button>,
        <Button
          key="save"
          varient="primary"
          htmlType="submit"
          form="create-staff-account"
          disabled={submitting}
        >
          保存
        </Button>,
      ]}
    >
      {submitting && <ModalLoading />}
      <Form id="create-staff-account" onSubmit={onSubmit}>
        <LoginIdCounter>{loginId.length}/30</LoginIdCounter>
        <InputWrapper>
          <ManagerKbnLabel label="ログインID" required />
          <Controller
            name="loginId"
            control={control}
            render={({ field }) => (
              <StyledTextInput
                {...field}
                hasError={!!errors.loginId}
                placeholder="ログインIDを入力してください"
                shouldTrim
              />
            )}
            rules={{
              required: "ログインIDは必須です",
              maxLength: {
                value: 30,
                message: "ログインIDは最大30文字で入力してください",
              },
              minLength: {
                value: 1,
                message: "ログインIDは最大1文字以上で入力してください",
              },
            }}
          />
          {errors.loginId && <ErrorText>{errors.loginId.message}</ErrorText>}
          <LoginIdAttension>
            ※
            1文字以上30文字以内、半角英数字及び記号（「@」「.」「-」「_」）を使用できます。
            <span>設定後は編集できません</span>
          </LoginIdAttension>
        </InputWrapper>
        <InputWrapper>
          <ManagerKbnLabel label="氏名" required />
          <Controller
            name="staffName"
            control={control}
            render={({ field }) => (
              <StyledHalfTextInput
                {...field}
                hasError={!!errors.staffName}
                placeholder="氏名を入力してください"
                shouldTrim
              />
            )}
            rules={{
              required: "氏名は必須です",
              maxLength: {
                value: 30,
                message: "30文字以内で入力してください。",
              },
            }}
          />
          {errors.staffName && (
            <ErrorText>{errors.staffName.message}</ErrorText>
          )}
        </InputWrapper>
        <InputWrapper>
          <ManagerKbnLabel label="フリガナ" required />
          <Controller
            name="staffKana"
            control={control}
            render={({ field }) => (
              <StyledHalfTextInput
                {...field}
                hasError={!!errors.staffKana}
                placeholder="フリガナを入力してください"
                shouldTrim
              />
            )}
            rules={{
              required: "フリガナは必須です",
              maxLength: {
                value: 30,
                message: "30文字以内で入力してください。",
              },
              pattern: {
                value: FURIGANA_REGEXP,
                message: "カタカナで入力してください",
              },
            }}
          />
          {errors.staffKana && (
            <ErrorText>{errors.staffKana.message}</ErrorText>
          )}
        </InputWrapper>
        <InputWrapper>
          <ManagerKbnLabel label="権限" required />
          <Flex gap={20} align="center">
            <Controller
              name="managerKbn"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Segmented
                  options={managerKbnList.map((kbn) => ({
                    label: kbn.title,
                    value: kbn.id,
                  }))}
                  value={value}
                  onChange={(e) => {
                    onChange(e);
                    resetPermissions();
                  }}
                />
              )}
            />
            <Controller
              name="staffType"
              control={control}
              render={({ field: { onChange } }) => (
                <Checkbox onChange={onChange}>医師</Checkbox>
              )}
            />
          </Flex>
        </InputWrapper>
        <InputWrapper>
          <ManagerKbnLabel label="医籍登録番号" />
          <Controller
            name="medicalLicenseNo"
            control={control}
            render={({ field }) => (
              <StyledHalfHalfTextInput
                {...field}
                hasError={!!errors.medicalLicenseNo}
                placeholder="123456"
                shouldTrim
                disabled={currentManagerKbn === ManagerKbn.COMMON}
              />
            )}
            rules={{
              pattern: {
                value: /^\d{6}$/,
                message: "6桁の数値で入力してください",
              },
            }}
          />
          {errors.medicalLicenseNo && (
            <ErrorText>{errors.medicalLicenseNo.message}</ErrorText>
          )}
        </InputWrapper>
        <InputWrapper>
          <ManagerKbnLabel label="麻薬施用者免許証番号" />
          <Controller
            name="mayakuLicenseNo"
            control={control}
            render={({ field }) => (
              <StyledHalfHalfTextInput
                {...field}
                hasError={!!errors.mayakuLicenseNo}
                placeholder="1234567"
                shouldTrim
                disabled={currentManagerKbn === ManagerKbn.COMMON}
              />
            )}
            rules={{
              pattern: {
                value: /^\d{7}$/,
                message: "7桁の数値で入力してください",
              },
            }}
          />
          {errors.mayakuLicenseNo && (
            <ErrorText>{errors.mayakuLicenseNo.message}</ErrorText>
          )}
        </InputWrapper>

        <InputWrapper>
          {currentManagerKbn === ManagerKbn.MANAGER ||
          currentManagerKbn === ManagerKbn.OWNER ? (
            <>
              <ManagerKbnText margin="0 0 8px">管理者</ManagerKbnText>
              <List>
                {adminPermissionList.map((value) => (
                  <ListItem key={value.id}>
                    <SvgIconTick />
                    <p>{value.detail}</p>
                  </ListItem>
                ))}
              </List>
              <ManagerKbnText margin="12px 0 4px">一般</ManagerKbnText>
              <List>
                {permissionTypeList.map((value) => (
                  <ListItem key={value.functionCd}>
                    <SvgIconTick />
                    <p>{value.describe}</p>
                  </ListItem>
                ))}
              </List>
            </>
          ) : (
            <>
              <ManagerKbnText margin="0 0 8px">管理者</ManagerKbnText>
              <List>
                {adminPermissionList.map((value) => (
                  <ListItem key={value.id}>
                    <Checkbox disabled>{value.detail}</Checkbox>
                  </ListItem>
                ))}
              </List>
              <Controller
                name="permissions"
                control={control}
                render={({ field: { onChange, value: groupValues } }) => (
                  <Checkbox.Group
                    onChange={onChange}
                    defaultValue={groupValues}
                  >
                    <ManagerKbnText margin="12px 0 4px">一般</ManagerKbnText>
                    <List>
                      {permissionTypeList.map((item) => (
                        <ListItem key={item.functionCd}>
                          <Checkbox
                            value={item.functionCd}
                            checked={groupValues.some(
                              (v) => v === item.functionCd,
                            )}
                          >
                            {item.describe}
                          </Checkbox>
                        </ListItem>
                      ))}
                    </List>
                  </Checkbox.Group>
                )}
              />
            </>
          )}
        </InputWrapper>
      </Form>
    </Modal>
  );
};
