import { Checkbox, Flex, Radio as AntdRadio } from "antd";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Form as CustomForm } from "@/components/functional/Form";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { SvgIconTick } from "@/components/ui/Icon/IconTick";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { Radio } from "@/components/ui/Radio";
import { Segmented } from "@/components/ui/Segmented";
import { TextInput } from "@/components/ui/TextInput";
import { ManagerKbn, managerKbnList, StaffType } from "@/constants/account";

import {
  pharmacyAdminPermissionList,
  pharmacyPermissionTypeList,
} from "../constants";
import { usePharmacyCreateStaff } from "../hooks/useCreatePharmacyStaff";

const Form = styled(CustomForm)`
  width: 100%;
  padding: 24px;
`;

const InputWrapper = styled.div`
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const LoginIdAttension = styled.p`
  font-size: 12px;
  line-height: 12px;
  padding-top: 4px;

  > span {
    color: #e74c3c;
  }
`;

const StyledTextInput = styled(TextInput)``;

const StyledHaifTextInput = styled(TextInput)`
  width: 50%;
`;

const ErrorText = styled(CommonErrorText)`
  padding-top: 4px;
`;

const ManagerKbnText = styled.p`
  font-size: 14px;
  color: #6a757d;
  margin-bottom: 12px;
`;

const List = styled.ul`
  display: flex;
  flex-wrap: wrap;
  width: 100%;
`;

const ListItem = styled.li`
  display: flex;
  width: 50%;
  margin-bottom: 10px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 2px;
`;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (loginId: string, password: string) => void;
};

export const CreatePharmacyAccountModal: React.FC<Props> = ({
  isOpen,
  onClose,
  onComplete,
}) => {
  const {
    submitting,
    onSubmit,
    errors,
    control,
    currentManagerKbn,
    currentStaffType,
    resetStaffType,
  } = usePharmacyCreateStaff(onComplete);

  return (
    <Modal
      isOpen={isOpen}
      onCancel={onClose}
      title="利用者の登録"
      width={760}
      centered
      footer={[
        <Button key="cancel" varient="tertiary" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="save"
          varient="primary"
          htmlType="submit"
          form="create-staff-account"
          disabled={submitting}
        >
          保存
        </Button>,
      ]}
    >
      {submitting && <ModalLoading />}
      <Form id="create-staff-account" onSubmit={onSubmit}>
        <InputWrapper>
          <StyledLabel label="ログインID" required />
          <Controller
            name="loginId"
            control={control}
            render={({ field }) => (
              <StyledTextInput
                {...field}
                hasError={!!errors.loginId}
                shouldTrim
              />
            )}
            rules={{
              required: "ログインIDは必須です",
              maxLength: {
                value: 30,
                message: "ログインIDは最大30文字で入力してください",
              },
              minLength: {
                value: 1,
                message: "ログインIDは最大1文字以上で入力してください",
              },
            }}
          />

          {errors.loginId && <ErrorText>{errors.loginId.message}</ErrorText>}
          <LoginIdAttension>
            ※
            1文字以上30文字以内、半角英数字及び記号（「@」「.」「-」「_」）を使用できます。
            <span>設定後は編集できません</span>
          </LoginIdAttension>
        </InputWrapper>
        <InputWrapper>
          <StyledLabel label="氏名" required />
          <Controller
            name="staffName"
            control={control}
            render={({ field }) => (
              <StyledHaifTextInput
                {...field}
                hasError={!!errors.staffName}
                placeholder="氏名を入力してください"
                shouldTrim
              />
            )}
            rules={{
              required: "氏名は必須です",
              maxLength: {
                value: 30,
                message: "30文字以内で入力してください。",
              },
            }}
          />
          {errors.staffName && (
            <ErrorText>{errors.staffName.message}</ErrorText>
          )}
        </InputWrapper>
        <InputWrapper>
          <StyledLabel label="フリガナ" required />
          <Controller
            name="staffKana"
            control={control}
            render={({ field }) => (
              <StyledHaifTextInput
                {...field}
                hasError={!!errors.staffKana}
                shouldTrim
              />
            )}
            rules={{
              required: "フリガナは必須です",
              maxLength: {
                value: 30,
                message: "30文字以内で入力してください。",
              },
              pattern: {
                value: /^[ァ-ンぁ-んー ]+$/,
                message: "フリガナを入力してください",
              },
            }}
          />
          {errors.staffKana && (
            <ErrorText>{errors.staffKana.message}</ErrorText>
          )}
        </InputWrapper>
        <InputWrapper>
          <InputLabel label="権限" required />
          <Flex gap={20} align="center">
            <Controller
              name="managerKbn"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Segmented
                  options={managerKbnList.map((kbn) => ({
                    label: kbn.title,
                    value: kbn.id,
                  }))}
                  value={value}
                  onChange={(e) => {
                    onChange(e);
                    resetStaffType();
                  }}
                />
              )}
            />
            <Controller
              name="staffType"
              control={control}
              render={({ field }) => (
                <AntdRadio.Group {...field}>
                  <Radio value={StaffType.Office_Pharmacist}>本店</Radio>
                  {/* <Radio
                    value={JOB_PHARMACIST_HOME_CODE}
                    disabled={currentManagerKbn !== ManagerKbn.COMMON}
                  >
                    在宅
                  </Radio> */}
                </AntdRadio.Group>
              )}
            />
          </Flex>
        </InputWrapper>

        <InputWrapper>
          {currentManagerKbn === ManagerKbn.MANAGER ||
          currentManagerKbn === ManagerKbn.OWNER ? (
            <>
              <ManagerKbnText>管理者</ManagerKbnText>
              <List>
                {pharmacyAdminPermissionList.map((value) => (
                  <ListItem key={value.id}>
                    <SvgIconTick />
                    <p>{value.detail}</p>
                  </ListItem>
                ))}
              </List>
              <ManagerKbnText>一般</ManagerKbnText>
              <List>
                {pharmacyPermissionTypeList.map((value) => (
                  <ListItem key={value.functionCd}>
                    <SvgIconTick />
                    <p>{value.describe}</p>
                  </ListItem>
                ))}
              </List>
            </>
          ) : (
            <>
              <ManagerKbnText>管理者</ManagerKbnText>
              <List>
                {pharmacyAdminPermissionList.map((value) => (
                  <ListItem key={value.id}>
                    <Checkbox disabled>{value.detail}</Checkbox>
                  </ListItem>
                ))}
              </List>
              <ManagerKbnText>一般</ManagerKbnText>
              <List>
                {pharmacyPermissionTypeList.map((item) => (
                  <ListItem key={item.functionCd}>
                    {currentStaffType === StaffType.Office_Pharmacist ? (
                      <>
                        <SvgIconTick />
                        <p>{item.describe}</p>
                      </>
                    ) : (
                      <Checkbox value={item.functionCd} disabled>
                        {item.describe}
                      </Checkbox>
                    )}
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </InputWrapper>
      </Form>
    </Modal>
  );
};
