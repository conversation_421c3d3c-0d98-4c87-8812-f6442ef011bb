import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import Link from "next/link";
import styled, { css } from "styled-components";
import dayjs from "dayjs";

import { SvgIconPatientInfo } from "@/components/ui/Icon/IconPatientInfo";
import { But<PERSON> } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";
import { formatAgeWithYearsAndMonths } from "@/utils/birthdate-format";
import {
  formatYYYYMMDDWithJapaneseEra,
  formatYYYYMMDDWithSlash,
} from "@/utils/datetime-format";
import { GotoMedicalFrom } from "@/constants/renkei";

import {
  ModalProvider as PaymentModalProvider,
  useModal as usePaymentModal,
} from "../KartePayment/providers/ModalProvider";
import {
  AccountingQueryProvider,
  useAccountingQuery,
} from "../KartePayment/providers/AccountingQueryProvider";
import { PaymentListModal } from "../KartePayment/ui/modal/PaymentListModal";

import type { PatientSearchFormType } from "@/hooks/usePatientSearchForm";
import type { GetPatientsQuery } from "@/apis/gql/operations/__generated__/patient";
import type { UseFormSetValue } from "react-hook-form";
import type { TableColumnType } from "antd";
import type { ReactNode } from "react";

const Name = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
`;

const NameKana = styled.div`
  color: #243544;
  font-size: 12px;
`;

const StyledLink = styled(Link)`
  line-height: normal;
  text-decoration: none;
`;

const Badge = styled.div`
  background-color: #005bac;
  color: #fff;
  font-size: 10px;
  border-radius: 2px;
  padding: 2px 4px;
  width: 60px;
`;

const GenderBagde = styled.div<{ $gender: 1 | 2 }>`
  width: 16px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  border-radius: 2px;

  ${({ $gender }) => {
    if ($gender === 1) {
      return css`
        background-color: #006ec3;
      `;
    }

    if ($gender === 2) {
      return css`
        background-color: #f48e91;
      `;
    }

    return undefined;
  }}
`;

const IconLink = styled(Link)`
  display: inline-block;
  width: 20px;
  height: 20px;
`;

const Icon = styled(SvgIconPatientInfo)`
  display: inline-block;
  margin-left: 5px;
`;

const StyledTable = styled(Table)`
  table {
    border-collapse: collapse;
  }

  .ant-table-row {
    .ant-table-cell {
      border: 1px solid #e0e6ec;
    }
  }
`;

const PatientName = ({
  patientID,
  patientName,
  patientNameKana,
  gender,
}: NonNullable<GetPatientsQuery["getPatients"]["patients"]>[number]) => {
  const kartePath = `/karte/${patientID}?sinDate=${dayjs().format("YYYYMMDD")}`;
  const hasPatientName = !!patientName;

  return (
    <Flex justify="space-between" align="center">
      {hasPatientName ? (
        <div>
          <NameKana>{patientNameKana || ""}</NameKana>
          <Name>
            <StyledLink href={kartePath} target="_blank">
              {patientName || ""}
            </StyledLink>
            {gender === 1 ? (
              <GenderBagde $gender={1}>M</GenderBagde>
            ) : gender === 2 ? (
              <GenderBagde $gender={2}>F</GenderBagde>
            ) : (
              ""
            )}
          </Name>
        </div>
      ) : (
        <Name>
          <StyledLink
            href={`${kartePath}?from=${GotoMedicalFrom.Other}`}
            target="_blank"
          >
            {patientNameKana || ""}
          </StyledLink>
          {gender === 1 ? (
            <GenderBagde $gender={1}>M</GenderBagde>
          ) : gender === 2 ? (
            <GenderBagde $gender={2}>F</GenderBagde>
          ) : (
            ""
          )}
        </Name>
      )}
      <IconLink href={`${kartePath}?modal=open`} target="_blank">
        <Tooltip
          title="患者情報"
          arrow={false}
          color="rgba(80, 80, 80, 0.9)"
          overlayStyle={{
            fontSize: "12px",
          }}
          overlayInnerStyle={{
            borderRadius: "2px",
          }}
        >
          <Icon />
        </Tooltip>
      </IconLink>
    </Flex>
  );
};

const ActionButtonsWrapper = styled.div`
  display: flex;
  gap: 12px;
  justify-content: center;
`;

const StyledButton = styled(Button)`
  width: 100px;
  height: 28px;
`;

const ActionButtons: React.FC<{
  onClickReservation: () => void;
  onClickReception: () => void;
}> = ({ onClickReservation, onClickReception }) => {
  const { handleError } = useErrorHandler();
  const { handleOpenModal } = usePaymentModal();
  const { patient } = useAccountingQuery();

  const isPortalUser = typeof patient?.portalCustomerId === "number";

  const handleLinkClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isPortalUser) {
      // 新しいタブでリンク遷移
      window.open(
        `/karte/${patient.ptId}?openMessage=true&from=${GotoMedicalFrom.Message}`,
        "_blank",
      );
    } else {
      event.preventDefault(); // デフォルトのリンク遷移を防止
      const error = new Error("マップ会員以外にはメッセージを送信できません");
      handleError({
        error,
        commonMessage: "マップ会員以外にはメッセージを送信できません",
      }); // エラーメッセージの表示
    }
  };

  return (
    <ActionButtonsWrapper>
      <StyledButton varient="standard" onClick={handleLinkClick}>
        メッセージ
      </StyledButton>

      <StyledButton
        varient="standard"
        onClick={() => handleOpenModal("PAYMENT_LIST")}
      >
        会計一覧
      </StyledButton>

      <StyledButton varient="standard" onClick={onClickReservation}>
        予約
      </StyledButton>

      <StyledButton varient="secondary" onClick={onClickReception}>
        受付
      </StyledButton>
    </ActionButtonsWrapper>
  );
};

type Props = {
  patients: NonNullable<GetPatientsQuery["getPatients"]["patients"]>;
  onClickReservation: (
    patient: NonNullable<GetPatientsQuery["getPatients"]["patients"]>[number],
  ) => void;
  /* onFetchMore?: VoidFunction; */
  onClickReception: (
    patient: NonNullable<GetPatientsQuery["getPatients"]["patients"]>[number],
  ) => void;
  onFetchMore?: () => Promise<void>;
  handleSetValue?: UseFormSetValue<PatientSearchFormType>;
};

export const PatientSearchResultTable: React.FC<Props> = ({
  patients,
  onClickReservation,
  onClickReception,
  onFetchMore,
  handleSetValue,
}) => {
  const { infiniteRef } = useInfiniteScroll(() => {
    const last = patients[patients.length - 1];
    if (!last) {
      return;
    }

    handleSetValue?.("cursor", last.patientID.toString());
    void onFetchMore?.();
  });

  const columns: TableColumnType<
    NonNullable<GetPatientsQuery["getPatients"]["patients"]>[number]
  >[] = [
    {
      title: "患者番号",
      width: 80,
      render: (_, { portalCustomerId, patientNumber }) => (
        <Flex align="center" justify="center" vertical>
          {typeof patientNumber === "number" && <p>{patientNumber}</p>}
          {typeof portalCustomerId === "number" && <Badge>マップ会員</Badge>}
        </Flex>
      ),
    },
    {
      title: "氏名",
      width: 180,
      render: (
        _,
        { patientID, patientName, patientNameKana, portalCustomerId, gender },
      ) => (
        <PatientName
          patientID={patientID}
          patientName={patientName}
          patientNameKana={patientNameKana}
          portalCustomerId={portalCustomerId}
          gender={gender}
        />
      ),
    },
    {
      title: "生年月日",
      width: 200,
      align: "center",
      render: (_, { birthdate }) =>
        birthdate ? formatYYYYMMDDWithJapaneseEra(birthdate) : null,
    },
    {
      title: "年齢",
      width: 100,
      align: "center",
      render: (_, { birthdate }) =>
        birthdate ? formatAgeWithYearsAndMonths(birthdate) : null,
    },
    {
      title: "最終受診日",
      align: "center",
      width: 120,
      render: (_, { lastAppointmentDate }) =>
        lastAppointmentDate
          ? formatYYYYMMDDWithSlash(new Date(lastAppointmentDate))
          : null,
    },
    {
      title: "次回予約日",
      align: "center",
      width: 120,
      render: (_, { nextAppointmentDate }) =>
        nextAppointmentDate
          ? formatYYYYMMDDWithSlash(new Date(nextAppointmentDate))
          : null,
    },
    {
      title: "",
      align: "center",
      width: 400,
      render: (_, patient) => (
        <PaymentModalProvider>
          <AccountingQueryProvider
            raiinNo=""
            sinDate={Number(dayjs().format("YYYYMMDD"))}
            patient={{
              ptId: patient.patientID.toString(),
              sex: patient.gender ?? 0,
              birthday: Number(dayjs(patient.birthdate).format("YYYYMMDD")),
              name: patient.patientName ?? "",
              kanaName: patient.patientNameKana ?? "",
              ptNum: patient.patientNumber?.toString() ?? "",
              portalCustomerId: patient.portalCustomerId,
            }}
          >
            <ActionButtons
              onClickReservation={() => onClickReservation(patient)}
              onClickReception={() => onClickReception(patient)}
            />
            <PaymentListModal />
          </AccountingQueryProvider>
        </PaymentModalProvider>
      ),
    },
  ];

  return (
    <StyledTable
      dataSource={patients.map((pt) => ({
        ...pt,
        key: pt.patientID,
      }))}
      columns={columns}
      scroll={{
        y: window.innerHeight < 860 ? 360 : window.innerHeight - 520,
      }}
      components={{
        body: {
          row: ({
            children,
            className,
            ...props
          }: {
            children: ReactNode;
            className: string;
            "data-row-key": number;
          }) => {
            const isLastRow =
              props["data-row-key"] ===
              patients[patients.length - 1]?.patientID;

            const lastRowRef = isLastRow ? infiniteRef : undefined;

            return (
              <tr className={className} ref={lastRowRef}>
                {children}
              </tr>
            );
          },
        },
      }}
    />
  );
};
