import React, { useCallback, useEffect, useState } from "react";

import { Flex } from "antd";
import dayjs from "dayjs";
import { Controller, useFormContext } from "react-hook-form";
import styled from "styled-components";
import { omit } from "lodash";

import { SvgIconArrowPulldown } from "@/components/ui/Icon/IconArrowPulldown";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import {
  formatYYYYMMDDWithJapaneseEra,
  formatYYYYWithJapaneseEra,
} from "@/utils/datetime-format";
import { DatePicker, DatePickerSuffixClear } from "@/components/ui/DatePicker";
import { Checkbox } from "@/components/ui/Checkbox";
import { SystemHub } from "@/constants/confirm-online";
import { numberToDate, toDateNumber } from "@/utils/add-patient";
import { useReceptionConfirmOnlineCertification } from "@/hooks/add-patient/useReceptionConfirmOnlineCertification";
import { TextInput } from "@/components/ui/TextInput";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { AuditEventCode } from "@/constants/audit-log";
import { useAuditLog } from "@/hooks/useAuditLog";
import { RenderIf } from "@/utils/common/render-if";
import { useGetApiPatientInforGetPatientByIdLazyQuery } from "@/apis/gql/operations/__generated__/patient-infor";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { InputNumber } from "../../InputNumberOnly";
import { usePatientContext } from "../../Providers/PatientProvider";
import { CredentialsModal } from "../../ConfirmOnlineFlowModal/CredentialsModal";
import {
  DEFAULT_BIRTHDAY,
  getInsuranceCompareModal,
} from "../../ConfirmOnlineFlowModal/helper";

import { OnlineResultConverter } from "./helper";

import type { Dayjs } from "dayjs";
import type { FC } from "react";
import type { DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation } from "@/apis/gql/generated/types";
import type {
  ComparePatientState,
  OnlineConfirmationResults,
  PatientInfoFormType,
} from "@/types/patient";

const DATE_FORMAT = "YYYY-MM-DD";
const isKohi = /^12.{6}$/;
const HOKEN_SYA_NO_LENGTH = [4, 6, 8];

type Props = {
  setConfirmOnlineResults: (result: OnlineConfirmationResults) => void;
};

type PatientCompareData = {
  birthday: Dayjs;
  homePost: string;
  homeAddress1: string;
  name: string;
  kanaName: string;
  sex: number;
  setanusi: string;
};

export const OnlineCertificationForm: FC<Props> = ({
  setConfirmOnlineResults,
}) => {
  const {
    patientProps,
    changeConfirmingType,
    handleOpenModal,
    handleSetOnlineConfirmationHistoryData,
    selectedPatient,
    confirmingType,
    modal,
  } = usePatientContext();
  const { handleError } = useErrorHandler();

  const [comparePatientResult, setComparePatientResult] =
    useState<ComparePatientState>();
  const [resultOfQualifications, setResultOfQualifications] =
    useState<
      DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[]
    >();

  const { handleAuditLogMutation } = useAuditLog();
  const { control, watch, getValues, setValue } =
    useFormContext<PatientInfoFormType>();
  const [hokensyaNo] = [watch("onlineCertification.hokensyaNo")];
  const isInsurerNumberKohi = isKohi.test(hokensyaNo);
  const handleFormatHokenSyaNo = useCallback(
    (value: string) => {
      let valueStr = String(value);
      if (!valueStr || /^0+$/.test(valueStr)) {
        return setValue("onlineCertification.hokensyaNo", valueStr);
      }
      for (const length of HOKEN_SYA_NO_LENGTH) {
        if (valueStr.length <= length) {
          valueStr = valueStr.padStart(length, "0");
          return setValue("onlineCertification.hokensyaNo", valueStr);
        }
      }
    },
    [setValue],
  );

  const [getApiPatientInforGetPatientById] =
    useGetApiPatientInforGetPatientByIdLazyQuery({
      onError: (error) => handleError({ error }),
    });

  const setDataFromCompare = useCallback(
    (data: Partial<PatientCompareData>) => {
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined) {
          setValue(`ptInf.${key as keyof PatientInfoFormType["ptInf"]}`, value);
        }
      });
    },
    [setValue],
  );

  const handleSetPatientInfo = useCallback(
    (
      data: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
    ) => {
      const {
        birthday,
        homePost,
        homeAddress1,
        homeAddress2,
        name,
        kanaName,
        sex,
        setanusi,
      } = OnlineResultConverter.getPatientInfo(data)!;

      setValue("ptInf.birthday", birthday);
      setValue("ptInf.homePost", homePost ?? "");
      setValue("ptInf.homeAddress1", homeAddress1 ?? "");
      setValue("ptInf.homeAddress2", homeAddress2 ?? "");
      setValue("ptInf.name", name ?? "");
      setValue("ptInf.kanaName", kanaName ?? "");
      setValue("ptInf.sex", sex);
      setValue("ptInf.setanusi", setanusi ?? "");
    },
    [setValue],
  );

  const handleComparePtInfo = useCallback(
    async ({
      resultOfQualification,
    }: {
      resultOfQualification: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
    }) => {
      if (!resultOfQualification?.[0]) return;
      setResultOfQualifications([...resultOfQualification]);

      const patientFromXml = OnlineResultConverter.getPatientInfo(
        resultOfQualification[0],
      );
      const getPatientByIdQuery = await getApiPatientInforGetPatientById({
        variables: {
          ptId: selectedPatient?.patientID.toString(),
        },
      });

      const patientInfo =
        getPatientByIdQuery.data?.getApiPatientInforGetPatientById?.data?.data;

      const comparePatientResult = {
        name: getInsuranceCompareModal(
          patientInfo?.name,
          patientFromXml?.name,
          true,
        ),
        kanaName: getInsuranceCompareModal(
          patientInfo?.kanaName,
          patientFromXml?.kanaName,
          true,
        ),
        homePost: getInsuranceCompareModal(
          patientInfo?.homePost,
          patientFromXml?.homePost,
        ),
        address: getInsuranceCompareModal(
          `${patientInfo?.homeAddress1}${patientInfo?.homeAddress2}`,
          patientFromXml?.address,
        ),
        sex: getInsuranceCompareModal(patientInfo?.sex, patientFromXml?.sex),
        birthday: {
          ...getInsuranceCompareModal(
            patientInfo?.birthday,
            toDateNumber(patientFromXml.birthday),
          ),
          isMap:
            toDateNumber(patientFromXml.birthday) === DEFAULT_BIRTHDAY ||
            getInsuranceCompareModal(
              patientInfo?.birthday,
              toDateNumber(patientFromXml.birthday),
            ).isMap,
        },
        setanusi: getInsuranceCompareModal(
          patientInfo?.setanusi,
          patientFromXml?.setanusi,
        ),
      };

      setComparePatientResult({
        ...comparePatientResult,
        isMapAll: false,
      });
      handleOpenModal("CREDENTIALS");
    },
    [
      getApiPatientInforGetPatientById,
      handleOpenModal,
      selectedPatient?.patientID,
    ],
  );

  const {
    xmlString,
    handleConfirmOnline,
    ValidateOnlineConfirmationComponent,
  } = useReceptionConfirmOnlineCertification({
    ptId:
      confirmingType === "ADDING_PATIENT_MY_CARD"
        ? String(patientProps?.patientId)
        : "0",
    handleSetSelectedResult: (data, checkDate) => {
      setValue(
        "onlineCertification.qualificationConfirmationDate",
        numberToDate(Number(checkDate)),
      );
      if (!data) return;
      changeConfirmingType("ADDING_PATIENT_MY_INSURANCE");
      setConfirmOnlineResults({
        resultOfQualification: [data],
        xmlString,
      });
      if (selectedPatient?.portalCustomerId) {
        handleComparePtInfo({
          resultOfQualification: [data],
        });
        return;
      }
      handleSetPatientInfo(data);
    },
    onDone: (resultOfQualification) => {
      if (selectedPatient?.portalCustomerId) {
        handleComparePtInfo({
          resultOfQualification,
        });
      }
    },
    type: SystemHub.PatientInf,
    hasSelectPatient: true,
    callback: (res) => handleSetOnlineConfirmationHistoryData(res || null),
  });

  const handleConfirmOnlineBtnClicked = useCallback(() => {
    handleOpenModal("ONLINE_QUALIFICATION_VERIFICATION");
    handleAuditLogMutation({
      ptId: selectedPatient?.patientID.toString(),
      eventCd: AuditEventCode.OnlineQualificationConfirmationHistoryDisplay,
    });
  }, [handleAuditLogMutation, handleOpenModal, selectedPatient?.patientID]);

  useEffect(() => {
    if (isInsurerNumberKohi) {
      const [kigo, edaNo] = getValues([
        "onlineCertification.kigo",
        "onlineCertification.edaNo",
      ]);
      if (kigo || edaNo) {
        setValue("onlineCertification.kigo", "");
        setValue("onlineCertification.edaNo", "");
      }
    }
  }, [getValues, isInsurerNumberKohi, setValue]);

  return (
    <>
      <Section>
        <SectionTitle>オンライン資格確認</SectionTitle>
        <InputArea>
          <InputWrapper>
            <StyledInputLabel label="生年月日" />
            <Controller
              control={control}
              name="onlineCertification.birthdate"
              render={({ field }) => (
                <Flex gap={12}>
                  <DateOfBirthPicker
                    allowClear={false}
                    suffixIcon={<SvgIconArrowPulldown />}
                    picker="year"
                    value={field.value}
                    onChange={(value) => field.onChange(value)}
                    minDate={dayjs("1907-01-01", DATE_FORMAT)}
                    maxDate={dayjs()}
                    format={(date) =>
                      formatYYYYWithJapaneseEra(date.toString())
                    }
                    width={170}
                  />
                  <DateOfBirthPicker
                    allowClear={false}
                    suffixIcon={<SvgIconArrowPulldown />}
                    picker="month"
                    format="MM月"
                    minDate={dayjs("1907-01-01", DATE_FORMAT)}
                    maxDate={dayjs()}
                    value={field.value}
                    onChange={(value) => field.onChange(value)}
                  />
                  <DateOfBirthPicker
                    allowClear={false}
                    suffixIcon={<SvgIconArrowPulldown />}
                    picker="date"
                    format="DD日"
                    minDate={dayjs("1907-01-01", DATE_FORMAT)}
                    maxDate={dayjs()}
                    value={field.value}
                    onChange={(value) => field.onChange(value)}
                  />
                </Flex>
              )}
            />
          </InputWrapper>
        </InputArea>
        <InputArea>
          <InputWrapper>
            <StyledInputLabel label="保険者/負担者番号" />
            <Controller
              control={control}
              name="onlineCertification.hokensyaNo"
              render={({ field: { value, onChange } }) => (
                <StyledInput
                  value={value}
                  width={170}
                  maxLength={8}
                  onChange={onChange}
                  onBlur={() => handleFormatHokenSyaNo(value)}
                />
              )}
            />
          </InputWrapper>
          <InputWrapper>
            <StyledInputLabel label="記号" />
            <Controller
              control={control}
              name="onlineCertification.kigo"
              render={({ field }) => (
                <StyledInput
                  {...field}
                  disabled={isInsurerNumberKohi}
                  maxLength={20}
                />
              )}
            />
          </InputWrapper>
          <InputWrapper>
            <StyledInputLabel label="番号/受給者番号" />
            <Controller
              control={control}
              name="onlineCertification.bango"
              render={({ field }) => <StyledInput {...field} maxLength={20} />}
            />
          </InputWrapper>
          <InputWrapper>
            <StyledInputLabel label="枝番" />
            <Controller
              control={control}
              name="onlineCertification.edaNo"
              render={({ field }) => (
                <StyledInputNumber
                  {...field}
                  disabled={isInsurerNumberKohi}
                  maxLength={2}
                  onBlur={() => {
                    if (!field.value) return;
                    field.onChange(field.value.padStart(2, "0"));
                  }}
                />
              )}
            />
          </InputWrapper>
        </InputArea>
        <InputArea>
          <StyledFlex align="flex-end" justify="space-between">
            <InputWrapper>
              <StyledInputLabel label="確認年月日" />
              <Controller
                control={control}
                name="onlineCertification.qualificationConfirmationDate"
                render={({ field }) => (
                  <StyledDatePicker
                    value={field.value}
                    format={(date) =>
                      formatYYYYMMDDWithJapaneseEra(date.toDate())
                    }
                    suffixIcon={<SvgIconCalendar />}
                    inputReadOnly
                    allowClear={false}
                    open={false}
                  />
                )}
              />
            </InputWrapper>
            <Flex align="center">
              <InputWrapper>
                <Controller
                  control={control}
                  name="onlineCertification.limitConsFlg"
                  render={({ field: { value, onChange } }) => (
                    <Checkbox
                      checked={value === "1"}
                      onChange={(e) => {
                        const checked = e.target.checked;
                        onChange(checked ? "1" : "0");
                      }}
                    >
                      限度額適用認定証提供に同意する
                    </Checkbox>
                  )}
                />
              </InputWrapper>
              <ButtonWrapper>
                <ConfirmButton
                  varient="secondary"
                  shape="round"
                  disabled={
                    !hokensyaNo ||
                    !!Number(patientProps?.onlineConfirmationHistoryId ?? 0)
                  }
                  onClick={() => {
                    const { birthdate, limitConsFlg, ...hokenInfo } = omit(
                      getValues("onlineCertification"),
                      ["qualificationConfirmationDate"],
                    );

                    handleConfirmOnline({
                      birthdate: toDateNumber(birthdate).toString(),
                      limitConsFlg,
                      selectedHokenInf: {
                        ...hokenInfo,
                        hokenId: 0,
                      },
                    });
                  }}
                >
                  オンライン資格確認
                </ConfirmButton>
              </ButtonWrapper>
            </Flex>
          </StyledFlex>
        </InputArea>
        <BottomButtonWrapper>
          <Button varient="inline" onClick={handleConfirmOnlineBtnClicked}>
            オンライン資格確認履歴
          </Button>
        </BottomButtonWrapper>
      </Section>

      <ValidateOnlineConfirmationComponent />
      <RenderIf condition={modal.credentialsOpen && !!comparePatientResult}>
        <CredentialsModal
          isOpen={modal.credentialsOpen}
          onConfirm={(data) => {
            if (!data) return;
            setDataFromCompare(data);
          }}
          compareData={{ ...comparePatientResult! }}
          resultOfQualifications={resultOfQualifications}
        />
      </RenderIf>
    </>
  );
};

const Section = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e3e5;

  &:last-of-type {
    border-bottom: none;
  }
`;

const SectionTitle = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  margin-bottom: 16px;
`;

const InputArea = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const StyledInput = styled(TextInput).withConfig({
  shouldForwardProp: (prop) => prop !== "fullWidth",
})<{ fullWidth?: boolean; width?: number }>(({ fullWidth, width = 140 }) => ({
  width: fullWidth ? "100%" : `${width}px`,
}));

const InputWrapper = styled.div``;

const StyledInputLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledDatePicker = styled(DatePickerSuffixClear)`
  width: 230px;
  margin-right: 40px;
`;

const StyledFlex = styled(Flex)`
  width: 100%;
`;

const ButtonWrapper = styled.div`
  margin-left: auto;
`;

const ConfirmButton = styled(Button)`
  width: 160px;
`;

const DateOfBirthPicker = styled(DatePicker)<{ width?: number }>`
  width: ${({ width }) => `${width ?? 140}px`};
  padding: 4px 4px 4px 8px;
  box-sizing: border-box;

  .ant-picker-suffix {
    margin-inline-start: 0;
  }
`;

const BottomButtonWrapper = styled.div`
  margin-top: -16px;
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
`;

const StyledInputNumber = styled(InputNumber).withConfig({
  shouldForwardProp: (prop) => prop !== "fullWidth",
})<{ fullWidth?: boolean; width?: number }>(({ fullWidth, width = 140 }) => ({
  width: fullWidth ? "100%" : `${width}px`,
}));
