import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import styled from "styled-components";
import { fromPair<PERSON>, omit } from "lodash";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { RenderIf } from "@/utils/common/render-if";
import { useUpdateRefNo } from "@/hooks/add-patient/useUpdateRefNo";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";
import { numberToDate } from "@/utils/add-patient";

import { OnlineResultConverter } from "../NewPatientModal/first-step/helper";

import { CommonTable } from "./CommonTable";
import { MaidenNameModal } from "./MaidenNameModal";
import { DEFAULT_BIRTHDAY, getPatientCompareInfo } from "./helper";

import type { Dayjs } from "dayjs";
import type {
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  DomainModelsPatientInforPatientInforConfirmOnlineDtoInput,
} from "@/apis/gql/generated/types";
import type { ComparePatientState } from "@/types/patient";

const ModalBodyWrapper = styled.div`
  padding: 24px 24px 20px 24px;
`;

const ContentWrapperTitle = styled.div`
  margin-bottom: 20px;
`;

const BoxFooter = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
`;

const ContentWrapper = styled.div``;

type PatientData = {
  name: string;
  kanaName: string;
  birthday: string;
  sex: string;
  homeAddress1: string;
  homePost: string;
  setanusi: string;
};

type Props = {
  isOpen: boolean;
  onConfirm: (data?: Partial<PatientCompareData>) => void;
  compareData: ComparePatientState;
  ignoreField?: Array<keyof PatientData>;
  resultOfQualifications?: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
};

type StatusUpdate = Partial<{
  name: boolean;
  kanaName: boolean;
  birthday: boolean;
  sex: boolean;
  homePost: boolean;
  homeAddress1: boolean;
  setanusi: boolean;
}>;

const fieldNames = {
  name: "氏名",
  kanaName: "カナ",
  birthday: "生年月日",
  sex: "性別",
  homePost: "郵便番号",
  homeAddress1: "住所",
  setanusi: "世帯主",
};

type PatientCompareData = {
  birthday?: Dayjs;
  homePost: string;
  homeAddress1: string;
  name: string;
  kanaName: string;
  sex: number;
  setanusi: string;
};

export const CredentialsModal: React.FC<Props> = ({
  isOpen,
  onConfirm,
  compareData,
  ignoreField = [],
  resultOfQualifications,
}) => {
  const {
    modal,
    confirmingType,
    resetAllData,
    handleOpenModal,
    handleCloseModal,
    handleUpdateOnlineMasterData,
    handleUpdateOnlineInsuranceData,
    onlineInsuranceData,
    onlineMasterData,
    selectedPatient,
  } = usePatientContext();
  const [statusUpdate, setStatusUpdate] = useState<StatusUpdate>();
  const ref = useRef<{ updateMaidenName: boolean }>({
    updateMaidenName: false,
  });
  const { handleUpdateRefNo } = useUpdateRefNo({
    screenCode: SystemScreenCode.Reception,
    systemHub: SystemHub.Reception,
  });

  const onConfirmCompare = useCallback(
    (data?: DomainModelsPatientInforPatientInforConfirmOnlineDtoInput[]) => {
      if (!data) {
        onConfirm();
        return;
      }
      const dataComapre = fromPairs(
        data.map(({ fieldName, fieldValue }) => [fieldName, fieldValue]),
      );

      onConfirm({
        ...dataComapre,
        birthday: dataComapre.birthday
          ? numberToDate(Number(dataComapre.birthday))
          : undefined,
      });
    },
    [onConfirm],
  );

  const tableData = useMemo((): {
    currentInfo: Partial<PatientData>;
    newInfo: Partial<PatientData>;
    changeStatus: Record<keyof PatientData, boolean>;
  } => {
    const remapData = omit(
      {
        ...compareData,
        homeAddress1: { ...compareData.address },
      },
      ["isMapAll", "address"],
    );
    return {
      currentInfo: omit(
        getPatientCompareInfo(compareData, "value"),
        ignoreField,
      ),
      newInfo: omit(
        getPatientCompareInfo(compareData, "xmlValue"),
        ignoreField,
      ),
      changeStatus: Object.keys(omit(remapData)).reduce(
        (acc, key) => {
          acc[key as keyof PatientData] =
            !!remapData[key as keyof PatientData]?.isMap; // Ensure it’s a boolean
          return acc;
        },
        {} as Record<keyof PatientData, boolean>,
      ),
    };
  }, [compareData, ignoreField]);

  const handleUpdatePatientInfo = useCallback(
    (status: StatusUpdate) => {
      const ptInfo: DomainModelsPatientInforPatientInforConfirmOnlineDtoInput[] =
        [];
      const raw = omit(
        { ...compareData, homeAddress1: { ...compareData.address } },
        ["isMapAll", "address"],
      );

      Object.entries(raw).forEach(([fieldName, value]) => {
        if (!value.isMap && status[fieldName as keyof StatusUpdate]) {
          let fieldValue = value.xmlValue as typeof fieldName;
          switch (fieldName) {
            case "homePost":
              fieldValue = fieldValue?.replace(/-/g, "");
              break;
            case "sex":
            case "birthday":
              fieldValue =
                fieldValue === "情報なし"
                  ? String(DEFAULT_BIRTHDAY)
                  : String(fieldValue);
              break;
            case "homeAddress1": {
              const { homeAddress1, homeAddress2 } =
                OnlineResultConverter.getPatientAddress(fieldValue);
              fieldValue = homeAddress1;
              fieldName = "homeAddress1";
              if (homeAddress2.length) {
                ptInfo.push({
                  fieldName: "homeAddress2",
                  fieldValue: homeAddress2,
                });
              }
            }
          }

          ptInfo.push({
            fieldName,
            fieldValue,
          });
        }
      });

      switch (confirmingType) {
        case "CONFIRMING_HOKEN_MY_INSURANCE":
        case "CONFIRMING_KOHI_MY_INSURANCE":
        case "ADDING_HOKEN_MY_INSURANCE":
        case "ADDING_KOHI_MY_INSURANCE":
        case "EDITING_HOKEN_MY_INSURANCE":
        case "EDITING_KOHI_MY_INSURANCE":
          handleUpdateOnlineInsuranceData({
            patientInfo: [...ptInfo],
          });
          break;

        default:
          handleUpdateOnlineMasterData({
            patientInforConfirmOnlineDto: [...ptInfo],
          });
          break;
      }

      if (
        (!status.name && !status.kanaName) ||
        (tableData.changeStatus.name && tableData.changeStatus.kanaName)
      ) {
        handleCloseModal("CREDENTIALS");
        onConfirmCompare(ptInfo);
        return;
      }
      handleOpenModal("MAIDEN_NAME");
    },
    [
      compareData,
      confirmingType,
      handleCloseModal,
      handleOpenModal,
      handleUpdateOnlineInsuranceData,
      handleUpdateOnlineMasterData,
      onConfirmCompare,
      tableData.changeStatus.kanaName,
      tableData.changeStatus.name,
    ],
  );

  const handleCancel = useCallback(() => {
    handleCloseModal("MAIDEN_NAME");
    handleCloseModal("CREDENTIALS");
    resetAllData();
  }, [handleCloseModal, resetAllData]);

  useEffect(() => {
    if (!ref.current.updateMaidenName) return;
    if (
      !onlineInsuranceData?.ptKyuseiModel &&
      !onlineMasterData?.ptKyuseiModel
    ) {
      return;
    }
    onConfirmCompare(onlineMasterData?.patientInforConfirmOnlineDto);

    handleCloseModal("MAIDEN_NAME");
    handleCloseModal("CREDENTIALS");
  }, [
    handleCloseModal,
    onConfirm,
    onlineInsuranceData?.ptKyuseiModel,
    onlineMasterData?.patientInforConfirmOnlineDto,
    onlineMasterData?.ptKyuseiModel,
  ]);

  return (
    <>
      <Modal
        title="最新の資格情報があります"
        centered
        isOpen={isOpen}
        width={760}
        onCancel={handleCancel}
        centerFooterContent
        footer={
          <BoxFooter>
            <Button
              key="cancel"
              shape="round"
              varient="tertiary"
              onClick={handleCancel}
            >
              キャンセル
            </Button>
            <Button
              varient="primary"
              shape="round"
              onClick={() => {
                if (!statusUpdate) return;
                handleUpdatePatientInfo(statusUpdate);
              }}
            >
              反映
            </Button>
          </BoxFooter>
        }
      >
        <ModalBodyWrapper>
          <ContentWrapperTitle>
            <p>オンライン資格確認情報を院内管理に上書きします</p>
          </ContentWrapperTitle>
          <ContentWrapper>
            <CommonTable<Partial<PatientData>>
              {...tableData}
              fieldNames={fieldNames}
              sentDataToParent={setStatusUpdate}
              titleColumn={["院内管理", "反映後の情報"]}
            />
          </ContentWrapper>
        </ModalBodyWrapper>
      </Modal>
      <RenderIf condition={modal.maidenNameOpen}>
        <MaidenNameModal
          isOpen={modal.maidenNameOpen}
          onCancel={handleCancel}
          onConfirm={(endDate) => {
            if (!endDate) {
              onConfirmCompare(onlineMasterData?.patientInforConfirmOnlineDto);

              handleCloseModal("MAIDEN_NAME");
              handleCloseModal("CREDENTIALS");
              return;
            }

            ref.current.updateMaidenName = true;
            switch (confirmingType) {
              case "ADDING_PATIENT_MY_CARD":
              case "ADDING_RECEPTION_MY_CARD":
              case "VIEW_RESULT_MY_CARD":
                handleUpdateOnlineMasterData({
                  ptKyuseiModel: {
                    name: compareData.name.value,
                    kanaName: compareData.kanaName.value,
                    endDate,
                  },
                });
                break;

              default:
                handleUpdateOnlineInsuranceData({
                  ptKyuseiModel: {
                    name: compareData.name.value,
                    kanaName: compareData.kanaName.value,
                    endDate,
                  },
                });
                break;
            }
            if (!selectedPatient?.patientID) return;
            if (
              !ref.current.updateMaidenName ||
              (!onlineInsuranceData?.ptKyuseiModel &&
                !onlineMasterData?.ptKyuseiModel)
            )
              return;
            if (
              confirmingType &&
              [
                "CONFIRMING_HOKEN_MY_INSURANCE",
                "CONFIRMING_KOHI_MY_INSURANCE",
              ].includes(confirmingType)
            ) {
              handleUpdateRefNo({
                ptId: selectedPatient?.patientID.toString() || "0",
                ptNum: String(selectedPatient?.patientNumber),
                resultOfQualificationConfirmation: resultOfQualifications?.[0],
              });
            }
          }}
        />
      </RenderIf>
    </>
  );
};
