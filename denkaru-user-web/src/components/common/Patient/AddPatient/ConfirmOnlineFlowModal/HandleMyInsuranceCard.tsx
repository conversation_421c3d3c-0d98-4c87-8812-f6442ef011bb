import { memo, useCallback, useEffect, useState } from "react";

import { omit, pick } from "lodash";

import {
  useGetApiInsuranceMstGetHokenInfLazyQuery,
  useGetApiPatientInforGetKohiDetailLazyQuery,
  usePostApiPatientInforSaveInsuranceInfoMutation,
} from "@/apis/gql/operations/__generated__/insurance";
import { useGetApiPatientInforGetPatientByIdLazyQuery } from "@/apis/gql/operations/__generated__/patient-infor";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { numberToDate, toDateNumber } from "@/utils/add-patient";
import { RenderIf } from "@/utils/common/render-if";
import { MAX_DATE } from "@/constants/setting-master";
import { dateToNumber } from "@/utils/datetime-format";
import { usePublicExpenseSelectDefaultValue } from "@/hooks/add-patient/usePublicExpenseSelectDefaultValue";
import {
  HOKEN_ALLOWS_FIELD,
  HOKEN_MST_MODEL,
  HOKEN_MST_MODEL_SAVE_KOHI,
  HOKENSYA_MST_MODEL,
  SAMPLE_CONFIRM_DATE,
} from "@/constants/insurance";
import { usePostApiPatientInforSaveKohiMutation } from "@/apis/gql/operations/__generated__/reception";
import { Emitter } from "@/utils/event-emitter";
import { EMITTER_EVENT } from "@/constants/event";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";
import { useUpdateRefNo } from "@/hooks/add-patient/useUpdateRefNo";

import { OnlineResultConverter } from "../NewPatientModal/first-step/helper";
import { usePatientContext } from "../Providers/PatientProvider";
import { HandleKohiModal } from "../HandleKohiModal";
import { InsuranceCardModal } from "../InsuranceCardModal/InsuranceCardModal";
import { sDateToAge } from "../InsuranceCardModal/helper";

import { CompareIdentityModal } from "./CompareIdentityModal";
import { CompareInsuranceModal } from "./CompareInsuranceModal";
import { CredentialsModal } from "./CredentialsModal";
import {
  DEFAULT_BIRTHDAY,
  getInsuranceCompareModal,
  getKogakuValue,
} from "./helper";

import type { Dayjs } from "dayjs";
import type {
  HokenMst,
  InsuranceCardFormType,
  RegistrationPublicExpenseInforForm,
} from "@/types/insurance";
import type {
  DomainModelsInsuranceKohiInfModel,
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  DomainModelsPatientInforCheckKohiInfoDifferenceDto,
  DomainModelsPatientInforCheckPatientHokenInfoDifferenceDto,
  DomainModelsPatientInforPatientInforModel,
  EmrCloudApiResponsesInsuranceHokenInfDto,
} from "@/apis/gql/generated/types";
import type { ComparePatientState } from "@/types/patient";

const hokenKubunMap: Record<number | string, string> = {
  67: "退職",
  100: "国保",
  39: "後期",
};

type InsuranceCardInfo = InsuranceCardFormType & { hokenMst?: HokenMst };
type PublicExpenseInfo = RegistrationPublicExpenseInforForm & {
  hokenMst?: HokenMst;
};

export const HandleMyInsuranceCard = memo(() => {
  const {
    modal,
    confirmingType,
    handleOpenModal,
    selectedSinDate,
    selectedPatient,
    selectedInsurance,
    duplicateModal,
    changeConfirmingType,
    onlineInsuranceData,
    resetOnlineData,
  } = usePatientContext();
  const { handleError } = useErrorHandler();

  const [getApiPatientInforGetPatientById] =
    useGetApiPatientInforGetPatientByIdLazyQuery({
      onError: (error) => handleError({ error }),
    });

  const [patientDetail, setPatientDetail] =
    useState<DomainModelsPatientInforPatientInforModel>();
  const [getHokenInf] = useGetApiInsuranceMstGetHokenInfLazyQuery();
  const [getPublicExpenseInf] = useGetApiPatientInforGetKohiDetailLazyQuery();

  const [saveInsuranceInfo] = usePostApiPatientInforSaveInsuranceInfoMutation({
    onError: (error) => handleError({ error }),
    onCompleted: () => resetOnlineData(),
    refetchQueries: [
      "insuranceGetApiPatientInforGetHokenInfByPtId",
      "getApiPatientInforGetPatientById",
    ],
  });
  const [savePublicExpenseInfo] = usePostApiPatientInforSaveKohiMutation({
    onError: (error) => {
      handleError({ error });
    },
    onCompleted: () => changeConfirmingType(null),
    refetchQueries: [
      "getApiPatientInforGetKohiInfByPtId",
      "getApiPatientInforGetPatientById",
    ],
  });

  const [insuranceCardInfo, setInsuranceCardInfo] =
    useState<InsuranceCardInfo>();
  const [publicExpenseInfo, setPublicExpenseInfo] =
    useState<PublicExpenseInfo>();

  const [insuranceInfo, setInsuranceInfo] = useState<
    EmrCloudApiResponsesInsuranceHokenInfDto | DomainModelsInsuranceKohiInfModel
  >();

  const [comparePatientResult, setComparePatientResult] =
    useState<ComparePatientState>();
  const [compareInsuranceResult, setCompareInsuranceResult] =
    useState<DomainModelsPatientInforCheckPatientHokenInfoDifferenceDto>();
  const [comparePublicExpenseResult, setComparePublicExpenseResult] = useState<
    DomainModelsPatientInforCheckKohiInfoDifferenceDto & { isMarucho?: boolean }
  >();
  const [resultOfQualifications, setResultOfQualifications] =
    useState<
      DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[]
    >();
  const [checkDate, setCheckDate] = useState<Dayjs>();
  const [mapAll, setMapAll] = useState<{
    ptInfo?: boolean;
    insurance?: boolean;
  }>();

  const { handleUpdateRefNo } = useUpdateRefNo({
    screenCode: SystemScreenCode.Reception,
    systemHub: SystemHub.Reception,
  });

  const { publicExpenseSelectData } = usePublicExpenseSelectDefaultValue(
    {
      ptId: selectedPatient?.patientID.toString(),
      sinDate: selectedSinDate,
    },
    !!publicExpenseInfo,
  );

  const handleSaveConfirmOnline = useCallback(() => {
    const confirmDateUpdate = insuranceInfo?.confirmDateList
      ?.map((item) => ({
        ...omit(
          item,
          "isDeleted",
          "onlineConfirmationId",
          "ptId",
          "checkMachine",
        ),
      }))
      ?.find((item) => item.confirmDate === toDateNumber(checkDate));

    const newConfirmDates = {
      ...(confirmDateUpdate || SAMPLE_CONFIRM_DATE),
      confirmDate: toDateNumber(checkDate),
      hokenGrp: confirmingType ? 2 : 0,
    };

    switch (confirmingType) {
      case "CONFIRMING_HOKEN_MY_INSURANCE":
        saveInsuranceInfo({
          variables: {
            input: {
              hokenInf: {
                ...pick(insuranceInfo, HOKEN_ALLOWS_FIELD),
                confirmDates: [newConfirmDates],
                rousaiTenkis: [],
              },
              hokenMstModel: { ...HOKEN_MST_MODEL },
              hokensyaMstModel: { ...HOKENSYA_MST_MODEL },
              ...onlineInsuranceData,
              ptId: selectedPatient?.patientID,
              sinDate: selectedSinDate,
              patientInfo: onlineInsuranceData?.patientInfo ?? [],
            },
          },
        });
        break;

      case "CONFIRMING_KOHI_MY_INSURANCE":
        savePublicExpenseInfo({
          variables: {
            limitList: [],
            kohi: {
              ...omit(insuranceInfo, [
                "isEmptyModel",
                "hasDateConfirmed",
                "hokenName",
                "lastDateConfirmed",
                "isExpirated",
                "isExpired",
                "hasDateConfirmed",
                "isDeleted",
                "filingInfModels",
              ]),
              confirmDateList: [newConfirmDates],
              hokenMstModel: { ...HOKEN_MST_MODEL_SAVE_KOHI },
              limitListModel: [
                {
                  id: "0",
                  kohiId: 0,
                  sinDate: 0,
                  hokenPid: 0,
                  sortKey: "string",
                  raiinNo: "0",
                  futanGaku: 0,
                  totalGaku: 0,
                  biko: "string",
                  isDeleted: 0,
                  sort: 0,
                  totalMoney: 0,
                  seqNo: 0,
                },
              ],
            },
            ...onlineInsuranceData,
            ptId: selectedPatient?.patientID.toString(),
            patientInfo: onlineInsuranceData?.patientInfo ?? [],
          },
        });
        break;

      default:
        break;
    }
  }, [
    checkDate,
    confirmingType,
    insuranceInfo,
    onlineInsuranceData,
    saveInsuranceInfo,
    savePublicExpenseInfo,
    selectedPatient?.patientID,
    selectedSinDate,
  ]);

  const getCompareFieldInsuranceCard = useCallback(
    async (insuranceCardInfo?: InsuranceCardInfo) => {
      if (insuranceCardInfo) {
        return {
          hokensyaNo: insuranceCardInfo?.hokensyaNo,
          kigo: insuranceCardInfo?.kigo,
          bango: insuranceCardInfo?.bango,
          edaNo: insuranceCardInfo?.edaNo,
          honkeKbn: insuranceCardInfo?.honkeKbn,
          kofuDate: toDateNumber(insuranceCardInfo?.kofuDate),
          startDate: toDateNumber(insuranceCardInfo?.startDate),
          endDate: toDateNumber(insuranceCardInfo?.endDate),
          kogakuKbn: insuranceCardInfo?.kogakuKbn,
          hokenInfo:
            insuranceCardInfo.hokenMst?.hokenNo === 68 &&
            insuranceCardInfo.hokenMst?.hokenEdaNo === 0
              ? "該当"
              : "",
          hokenId: insuranceCardInfo.hokenId,
          seqNo: insuranceCardInfo.seqNo,
          houbetu: insuranceCardInfo.hokenMst?.houbetu,
        };
      }

      const hokenInfoQuery = await getHokenInf({
        variables: {
          hokenId: selectedInsurance?.hokenId,
          ptId: selectedPatient?.patientID.toString(),
        },
      });

      const hokenInf =
        hokenInfoQuery.data?.getApiInsuranceMstGetHokenInf?.data?.hokenInfList;

      setInsuranceInfo(hokenInf);

      return {
        hokensyaNo: hokenInf?.hokensyaNo,
        kigo: hokenInf?.kigo,
        bango: hokenInf?.bango,
        edaNo: hokenInf?.edaNo,
        honkeKbn: hokenInf?.honkeKbn,
        kofuDate: hokenInf?.kofuDate,
        startDate: hokenInf?.startDate,
        endDate: hokenInf?.endDate,
        kogakuKbn: hokenInf?.kogakuKbn?.toString(),
        hokenInfo:
          hokenInf?.hokenNo === 68 && hokenInf.hokenEdaNo === 0 ? "該当" : "",
        hokenId: hokenInf?.hokenId,
        houbetu: hokenInf?.houbetu,
        seqNo: hokenInf?.seqNo,
        hokenInfModel: { ...hokenInf },
      };
    },
    [getHokenInf, selectedInsurance?.hokenId, selectedPatient?.patientID],
  );

  const handleCompareInsuranceCard = useCallback(async () => {
    const data = resultOfQualifications?.[0];
    if (!data) return;

    let birthday = selectedPatient?.birthdate
      ? dateToNumber(new Date(selectedPatient.birthdate))
      : 0;

    const birthdayUpdated = onlineInsuranceData?.patientInfo?.find(
      (item) => item.fieldName === "birthday",
    );
    if (birthdayUpdated) {
      birthday = Number(birthdayUpdated.fieldValue || 0);
    }

    const hokenFromXml = OnlineResultConverter.getInsuranceCardValues(
      data,
      selectedSinDate,
      sDateToAge(birthday, selectedSinDate),
    );

    const hokenInf = await getCompareFieldInsuranceCard(insuranceCardInfo);

    const kogaku = getKogakuValue(
      sDateToAge(birthday, selectedSinDate),
      hokenInf.hokensyaNo || "",
      selectedSinDate,
      hokenInf.kogakuKbn,
    );

    const compareInsuranceResult = {
      hokensyaNo: getInsuranceCompareModal(
        hokenInf?.hokensyaNo,
        hokenFromXml?.hokensyaNo,
      ),
      kigo: getInsuranceCompareModal(hokenInf?.kigo, hokenFromXml?.kigo),
      bango: getInsuranceCompareModal(hokenInf?.bango, hokenFromXml?.bango),
      hokenEdaNo: getInsuranceCompareModal(
        hokenInf?.edaNo,
        hokenFromXml?.edaNo,
        false,
        true,
      ),
      honkeKbn: getInsuranceCompareModal(
        hokenInf?.honkeKbn,
        hokenFromXml?.honkeKbn,
        false,
        true,
      ),
      kofuDate: getInsuranceCompareModal(
        hokenInf?.kofuDate,
        toDateNumber(hokenFromXml?.kofuDate),
        false,
        true,
      ),
      startDate: getInsuranceCompareModal(
        hokenInf?.startDate,
        toDateNumber(hokenFromXml?.startDate),
        false,
        true,
      ),
      endDate: getInsuranceCompareModal(
        hokenInf?.endDate === MAX_DATE ? 0 : hokenInf?.endDate,
        toDateNumber(hokenFromXml?.endDate),
      ),
      kogakuKbn: getInsuranceCompareModal(
        Number(kogaku) ? kogaku : "",
        hokenFromXml?.kogaku,
      ),
      hokenInfo: getInsuranceCompareModal(
        hokenInf.hokenInfo,
        data?.insuredCardClassification === "05" ? "該当" : "",
      ),
    };

    if (Object.values(compareInsuranceResult).every((item) => item.isMap)) {
      setMapAll({
        insurance: true,
      });
      if (selectedPatient?.patientID) {
        handleUpdateRefNo({
          ptId: selectedPatient?.patientID.toString(),
          ptNum: String(selectedPatient.patientNumber),
          resultOfQualificationConfirmation: resultOfQualifications[0],
        });
      }
      return;
    }

    const formattedHokenId = hokenInf?.hokenId
      ? hokenInf?.hokenId?.toString().padStart(3, "0")
      : "";
    const hokenKubun = hokenKubunMap[hokenInf?.houbetu ?? ""] || "社保";
    const hokenName = `${formattedHokenId} ${hokenKubun}`;

    setCompareInsuranceResult({
      ...compareInsuranceResult,
      hokenName,
      hokenId: hokenInf?.hokenId,
      seqNo: hokenInf?.seqNo,
    });
    handleOpenModal("COMPARE_HOKEN");
  }, [
    getCompareFieldInsuranceCard,
    handleOpenModal,
    insuranceCardInfo,
    onlineInsuranceData?.patientInfo,
    resultOfQualifications,
    selectedPatient?.birthdate,
    selectedSinDate,
  ]);

  const getPublicExpenseInfo = useCallback(
    async (publicExpenseInfo?: PublicExpenseInfo) => {
      if (publicExpenseInfo) {
        return {
          futansyaNo: publicExpenseInfo?.futansyaNo,
          jyukyusyaNo: publicExpenseInfo?.jyukyusyaNo,
          startDate: toDateNumber(publicExpenseInfo?.startDate),
          endDate: toDateNumber(publicExpenseInfo?.endDate),
          gendogaku: publicExpenseInfo?.gendogaku,
          birthday: toDateNumber(publicExpenseInfo?.birthday),
          hokenId: selectedInsurance?.hokenId,
          seqNo: selectedInsurance?.seqNo,
          houbetu: publicExpenseInfo.hokenMst?.houbetu,
          hokenNameCd: publicExpenseInfo?.hokenMst?.hokenNameCd,
        };
      }

      const kohiInfoQuery = await getPublicExpenseInf({
        variables: {
          hokenId: selectedInsurance?.hokenId,
          ptId: selectedPatient?.patientID.toString(),
          seqNo: Number(selectedInsurance?.seqNo ?? 0),
          sinDate: selectedSinDate,
        },
      });

      const kohiInf =
        kohiInfoQuery.data?.getApiPatientInforGetKohiDetail?.data?.data;

      setInsuranceInfo(kohiInf);

      const hokenMst = publicExpenseSelectData.find(
        (item) =>
          item.uniqKey === `${kohiInf?.hokenEdaNo}__${kohiInf?.hokenNo}`,
      );

      let gendogaku = kohiInf?.gendoGaku;
      if (gendogaku === 0) {
        gendogaku = hokenMst?.monthLimitFutan;
      }

      return {
        futansyaNo: kohiInf?.futansyaNo,
        jyukyusyaNo: kohiInf?.jyukyusyaNo,
        startDate: kohiInf?.startDate,
        endDate: kohiInf?.endDate,
        gendogaku,
        birthday: kohiInf?.birthday,
        hokenId: kohiInf?.hokenId,
        seqNo: kohiInf?.seqNo,
        houbetu: kohiInf?.houbetu,
        hokenNameCd: hokenMst?.hokenNameCd,
        kohiInfModel: { ...kohiInf },
      };
    },
    [
      getPublicExpenseInf,
      publicExpenseSelectData,
      selectedInsurance?.hokenId,
      selectedInsurance?.seqNo,
      selectedPatient?.patientID,
      selectedSinDate,
    ],
  );

  const handleComparePublicExpense = useCallback(async () => {
    const data = resultOfQualifications?.[0];
    if (!data) return;
    const hokenFromXml = OnlineResultConverter.getPublicExpenseInfo(data);

    const kohiInf = await getPublicExpenseInfo(publicExpenseInfo);

    const comparePublicExpenseResult = {
      futansyaNo: getInsuranceCompareModal(
        kohiInf?.futansyaNo,
        hokenFromXml?.futansyaNo,
      ),
      jyukyusyaNo: getInsuranceCompareModal(
        kohiInf?.jyukyusyaNo,
        hokenFromXml?.jyukyusyaNo,
      ),
      startDate: getInsuranceCompareModal(
        kohiInf?.startDate,
        toDateNumber(hokenFromXml?.startDate),
      ),
      endDate: getInsuranceCompareModal(
        kohiInf?.endDate === MAX_DATE ? 0 : kohiInf?.endDate,
        toDateNumber(hokenFromXml?.endDate),
      ),
      gendogaku: getInsuranceCompareModal(
        kohiInf?.gendogaku,
        Number(hokenFromXml?.gendogaku),
        false,
        true,
      ),
      birthDay: getInsuranceCompareModal(
        kohiInf?.birthday,
        toDateNumber(hokenFromXml?.birthday),
      ),
    };
    if (Object.values(comparePublicExpenseResult).every((item) => item.isMap)) {
      setMapAll({
        insurance: true,
      });
      if (selectedPatient?.patientID) {
        handleUpdateRefNo({
          ptId: selectedPatient?.patientID.toString(),
          ptNum: String(selectedPatient.patientNumber),
          resultOfQualificationConfirmation: resultOfQualifications[0],
        });
      }
      return;
    }

    const formattedHokenId = kohiInf?.hokenId
      ? kohiInf?.hokenId?.toString().padStart(3, "0")
      : "";
    const kohiName = `${formattedHokenId} ${kohiInf.hokenNameCd}`;

    setComparePublicExpenseResult({
      ...comparePublicExpenseResult,
      kohiName,
      hokenId: kohiInf?.hokenId,
      seqNo: kohiInf?.seqNo,
    });
    handleOpenModal("COMPARE_IDENTITY");
  }, [
    getPublicExpenseInfo,
    handleOpenModal,
    publicExpenseInfo,
    resultOfQualifications,
  ]);

  const handleOpenNextModal = useCallback(async () => {
    switch (confirmingType) {
      case "CONFIRMING_HOKEN_MY_INSURANCE":
      case "EDITING_HOKEN_MY_INSURANCE":
      case "ADDING_HOKEN_MY_INSURANCE":
        return handleCompareInsuranceCard();
      case "CONFIRMING_KOHI_MY_INSURANCE":
      case "EDITING_KOHI_MY_INSURANCE":
      case "ADDING_KOHI_MY_INSURANCE":
        return handleComparePublicExpense();
      default:
        break;
    }
  }, [confirmingType, handleCompareInsuranceCard, handleComparePublicExpense]);

  const handleComparePtInfo = useCallback(
    async ({
      resultOfQualification,
      insuranceInfo,
      checkDate,
    }: {
      resultOfQualification: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
      insuranceInfo?: InsuranceCardInfo | PublicExpenseInfo;
      checkDate: string;
    }) => {
      switch (confirmingType) {
        case "ADDING_HOKEN_MY_INSURANCE":
        case "EDITING_HOKEN_MY_INSURANCE":
          setInsuranceCardInfo(insuranceInfo as InsuranceCardFormType);
          break;
        case "ADDING_KOHI_MY_INSURANCE":
        case "EDITING_KOHI_MY_INSURANCE":
          setPublicExpenseInfo(insuranceInfo as PublicExpenseInfo);
          break;

        default:
          break;
      }
      if (checkDate) {
        setCheckDate(numberToDate(Number(checkDate)));
      }
      if (!resultOfQualification?.[0]) return;
      setResultOfQualifications([...resultOfQualification]);
      const patientFromXml = OnlineResultConverter.getPatientInfo(
        resultOfQualification[0],
      );
      const getPatientByIdQuery = await getApiPatientInforGetPatientById({
        variables: {
          ptId: selectedPatient?.patientID.toString(),
        },
      });

      const patientInfo =
        getPatientByIdQuery.data?.getApiPatientInforGetPatientById?.data?.data;

      setPatientDetail(patientDetail);

      const comparePatientResult = {
        name: getInsuranceCompareModal(
          patientInfo?.name,
          patientFromXml?.name,
          true,
        ),
        kanaName: getInsuranceCompareModal(
          patientInfo?.kanaName,
          patientFromXml?.kanaName,
          true,
        ),
        homePost: getInsuranceCompareModal(
          patientInfo?.homePost,
          patientFromXml?.homePost,
        ),
        address: getInsuranceCompareModal(
          `${patientInfo?.homeAddress1}${patientInfo?.homeAddress2}`,
          patientFromXml?.address,
        ),
        sex: getInsuranceCompareModal(patientInfo?.sex, patientFromXml?.sex),
        birthday: {
          ...getInsuranceCompareModal(
            patientInfo?.birthday,
            toDateNumber(patientFromXml.birthday),
          ),
          isMap:
            toDateNumber(patientFromXml.birthday) === DEFAULT_BIRTHDAY ||
            getInsuranceCompareModal(
              patientInfo?.birthday,
              toDateNumber(patientFromXml.birthday),
            ).isMap,
        },
        setanusi: getInsuranceCompareModal(
          patientInfo?.setanusi,
          patientFromXml?.setanusi,
        ),
      };

      if (Object.values(comparePatientResult).every((item) => item.isMap)) {
        setMapAll({
          ptInfo: true,
        });
        return;
      }
      setComparePatientResult({
        ...comparePatientResult,
        isMapAll: false,
      });
      handleOpenModal("CREDENTIALS");
    },
    [
      confirmingType,
      getApiPatientInforGetPatientById,
      handleOpenModal,
      selectedPatient?.patientID,
    ],
  );

  const RenderDuplicateModal = useCallback(() => {
    const insuranceCard = Object.entries(duplicateModal.insuranceCard).map(
      ([key]) => <InsuranceCardModal key={key} modalKey={Number(key)} />,
    );

    const publicExpense = Object.entries(duplicateModal.publicExpense).map(
      ([key]) => <HandleKohiModal key={key} modalKey={Number(key)} />,
    );

    return (
      <>
        {insuranceCard}
        {publicExpense}
      </>
    );
  }, [duplicateModal.insuranceCard, duplicateModal.publicExpense]);

  useEffect(() => {
    if (!mapAll?.ptInfo) return;
    if (!resultOfQualifications?.length) return;
    if (onlineInsuranceData && checkDate) {
      handleOpenNextModal();
    }

    return () => {
      setMapAll({
        ptInfo: false,
        insurance: false,
      });
    };
  }, [
    checkDate,
    handleOpenNextModal,
    mapAll?.ptInfo,
    onlineInsuranceData,
    resultOfQualifications?.length,
  ]);

  useEffect(() => {
    if (!mapAll?.insurance) return;
    if (!onlineInsuranceData) return;
    if (!insuranceInfo) return;
    if (!checkDate) return;
    handleSaveConfirmOnline();

    return () => {
      setMapAll({
        ptInfo: false,
        insurance: false,
      });
    };
  }, [
    checkDate,
    handleSaveConfirmOnline,
    insuranceInfo,
    mapAll?.insurance,
    onlineInsuranceData,
  ]);

  useEffect(() => {
    Emitter.once(EMITTER_EVENT.COMPARE_PATIENT_INFO, (res) =>
      handleComparePtInfo(res),
    );

    return () => {
      Emitter.off(EMITTER_EVENT.COMPARE_PATIENT_INFO, (res) =>
        handleComparePtInfo(res),
      );
    };
  }, [handleComparePtInfo]);

  return (
    <>
      <RenderIf condition={modal.credentialsOpen && !!comparePatientResult}>
        <CredentialsModal
          isOpen={modal.credentialsOpen}
          onConfirm={handleOpenNextModal}
          compareData={{ ...comparePatientResult! }}
          resultOfQualifications={resultOfQualifications}
        />
      </RenderIf>
      <RenderIf condition={modal.compareHokenOpen && !!compareInsuranceResult}>
        <CompareInsuranceModal
          isOpen={modal.compareHokenOpen}
          {...compareInsuranceResult!}
          checkDate={checkDate}
          onSkip={() => handleSaveConfirmOnline()}
          resultOfQualifications={resultOfQualifications}
        />
      </RenderIf>
      <RenderIf
        condition={
          modal.compareIdentityOpen &&
          !!comparePublicExpenseResult &&
          !comparePublicExpenseResult?.isMarucho
        }
      >
        <CompareIdentityModal
          isOpen={modal.compareIdentityOpen}
          {...comparePublicExpenseResult}
          checkDate={checkDate}
          publicExpenseSelectData={publicExpenseSelectData}
          resultOfQualifications={resultOfQualifications}
          onSkip={() => handleSaveConfirmOnline()}
        />
      </RenderIf>
      <RenderDuplicateModal />
    </>
  );
});
