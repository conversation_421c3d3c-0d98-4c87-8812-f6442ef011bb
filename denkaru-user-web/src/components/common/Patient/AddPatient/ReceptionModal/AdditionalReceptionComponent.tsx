import React, { useEffect, useMemo, useState } from "react";

import Flex from "antd/es/flex";
import Select from "antd/es/select";
import dayjs from "dayjs";
import { find, get, some } from "lodash";
import { Controller, FormProvider } from "react-hook-form";
import styled from "styled-components";

import { Form } from "@/components/functional/Form";
import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";
import { useGetSettings } from "@/hooks/useGetSettings";
import { useSession } from "@/hooks/useSession";
import { RenderIf } from "@/utils/common/render-if";
import { formatYYYYMMDDWithJapaneseEra } from "@/utils/datetime-format";
import {
  FIRST_VISIT,
  FIRST_VISIT_VALUES,
  PRESCRIPTION_TYPE,
  TIMEZONE,
  TIMEZONE_CHILDREN,
} from "@/constants/reception";
import { useAddReceptionDefaultValue } from "@/hooks/add-patient/useAddReceptionDefaultValue";
import { useGetPatientInfoById } from "@/hooks/add-patient/useGetPatientInfoById";
import { useGetReceptionDetail } from "@/hooks/add-patient/useGetReceptionDetail";
import { useListUser } from "@/hooks/add-patient/useListUser";
import { useTreatmentDepartmentListQuery } from "@/hooks/add-patient/useTreatmentDepartmentListQuery";
import { useAddReception } from "@/hooks/add-patient/useAddReception";
import { HOKEN_CONSTANTS } from "@/constants/insurance";
import { Checkbox } from "@/components/ui/Checkbox";
import { useGetSystemConfQuery } from "@/hooks/add-patient/useGetSystemConfQuery";

import { HokenPatternConfirmModal } from "../HokenPatternConfirmModal";
import { HokenPatternErrorModal } from "../HokenPatternErrorModal";
import { usePatientContext } from "../Providers/PatientProvider";

import { FirstVisitFeeModal } from "./FirstVisitFeeModal";
import { HokenInfo } from "./HokenInfo";
import { LabelsPopover } from "./LabelsPopover";
import {
  getHokenPatternReceptionForm,
  isSomeInsuranceInHokenPatternExpired,
} from "./helper";
import { KohiInfo } from "./KohiInfo";

import type { ReceptionComponentProps } from "@/types/reception";
import type { Option } from "@/types/insurance";

export const AdditionalReceptionComponent: React.FC<
  ReceptionComponentProps
> = ({
  onDone,
  onClose,
  setLoading,
  ptId,
  raiinNo,
  isEdit = false,
  setDisabledButtonAdd,
  drawerMode = false,
}) => {
  const { selectedSinDate } = usePatientContext();
  const { settingList } = useGetSettings();
  const { systemConfList } = useGetSystemConfQuery();

  const [lastComeDate, setLastComeDate] = useState<number>();
  const [firstTimeConfirmDays, setFirstTimeConfirmDays] = useState<number>();
  const [timeZoneList, setTimeZoneList] = useState<Option<number>[]>(TIMEZONE);

  const { session } = useSession();

  const { receptionDetail } = useGetReceptionDetail(raiinNo, !raiinNo);

  const { patientDetail } = useGetPatientInfoById({ ptId }, !ptId);
  const {
    methods,
    onSubmit,
    labelList,
    insurancesData,
    refKohi,
    handleConfirmCreateHokenPattern,
    hokenPatternValidation,
    setHokenPatternValidation,
    hokenPatientList,
    refHokenPattern,
    setClickConfirmDate,
  } = useAddReception({
    onDone,
    onClose,
    setLoading,
    ptId,
    receptionDetail,
    patientDetail,
    isEdit,
    setDisabledButtonAdd,
    drawerMode,
  });

  const { listUserOptions: listUser, listDoctorsDropdown: listDoctors } =
    useListUser({
      isDoctorOnly: true,
      sinDate: selectedSinDate,
    });

  const { defaultValue, isNotContinuousDiseases, isLinkCard } =
    useAddReceptionDefaultValue(
      {
        ptId,
        sinDate: selectedSinDate,
        hokenId: 0,
        requestFrom: 0,
        birthDay: patientDetail?.birthday,
        isLastVisit: true,
        raiinNo: raiinNo || "0",
      },
      !patientDetail,
      selectedSinDate,
    );

  const { listTreatmentOptions } = useTreatmentDepartmentListQuery();

  const {
    handleSubmit,
    setValue,
    formState: { isSubmitting, isSubmitted },
    control,
    trigger,
    watch,
  } = methods;

  const ptBirthday = useMemo(() => {
    const birthday = patientDetail?.birthday;
    if (birthday) {
      const birthdate = dayjs(String(birthday), "YYYYMMDD");
      const sinDate = dayjs(String(selectedSinDate), "YYYYMMDD");
      return `${formatYYYYMMDDWithJapaneseEra(String(birthdate))}　${sinDate.diff(birthdate, "years")}歳`;
    }
    return null;
  }, [patientDetail?.birthday, selectedSinDate]);

  const selectedHoken = watch("hokenPattern.hokenId");

  const hokenInfo = useMemo(() => {
    return insurancesData.listHoken.find(
      (item) => item.hokenId === selectedHoken,
    );
  }, [selectedHoken, insurancesData.listHoken]);

  const isJihi100Percent =
    hokenInfo?.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_108;

  const hasPrescriptionSection = useMemo(
    () => some(systemConfList, { grpCd: 100040, grpEdaNo: 1, val: 1 }),
    [systemConfList],
  );

  // get default syosaisinKbn
  useEffect(() => {
    const lastComeDate =
      defaultValue?.getApiReceptionGetLastRaiinInfs?.data?.data?.[0]?.sinDate;

    const defaultIsFirstVisitTime =
      get(find(settingList, { grpCd: 1007, grpEdaNo: 0 }), "systemConf.val") ??
      0;

    const defaultFirstTimeConfirmDays =
      get(find(settingList, { grpCd: 1013, grpEdaNo: 0 }), "systemConf.val") ??
      90;

    if (defaultIsFirstVisitTime === 0) {
      setValue("syosaisinKbn", FIRST_VISIT_VALUES.PLEASE_SELECT);
      return;
    }

    if (isNotContinuousDiseases) {
      setValue("syosaisinKbn", FIRST_VISIT_VALUES.FIRST_VISIT);
    } else {
      const isEligibleForFirstTimeConfirm =
        Number(
          dayjs(String(lastComeDate))
            .add(defaultFirstTimeConfirmDays, "day")
            .format("YYYYMMDD"),
        ) <= Number(selectedSinDate);

      if (lastComeDate && lastComeDate > 0 && isEligibleForFirstTimeConfirm) {
        setLastComeDate(lastComeDate);
        setFirstTimeConfirmDays(defaultFirstTimeConfirmDays);
      }
      setValue("syosaisinKbn", FIRST_VISIT_VALUES.FOLLOW_UP_VISIT);
    }
  }, [
    defaultValue?.getApiReceptionGetLastRaiinInfs?.data?.data,
    isNotContinuousDiseases,
    selectedSinDate,
    setValue,
    settingList,
  ]);

  // get default doctor
  useEffect(() => {
    const lastTanto =
      defaultValue?.getApiReceptionGetLastRaiinInfs?.data?.data?.[0]?.tantoId;

    if (!listUser) return;
    const currentUserIsDoctor = listUser.find(
      (item) => item.value === session.staffInfo?.staffId,
    );
    const previousDoctorId = listUser.find((item) => item.value === lastTanto);

    if (listUser.length === 1 && listUser[0]?.value) {
      // if doctor list have only one doctor
      setValue("doctorId", listUser[0].value);
      return;
    }

    if (currentUserIsDoctor) {
      // if current user is doctor
      setValue("doctorId", currentUserIsDoctor.value);
      return;
    }

    if (previousDoctorId) {
      // if have previous doctor
      setValue("doctorId", previousDoctorId.value);
      return;
    }
  }, [
    defaultValue?.getApiReceptionGetLastRaiinInfs?.data?.data,
    listUser,
    session.staffInfo?.staffId,
    setValue,
  ]);

  // get default jibanKbn
  useEffect(() => {
    const defaultSelectedTime =
      defaultValue?.getApiReceptionGetDefaultSelectedTime?.data?.data;

    if (patientDetail?.birthday) {
      if (defaultSelectedTime?.isPatientChildren) {
        setTimeZoneList(TIMEZONE_CHILDREN);
      }
      if (typeof defaultSelectedTime?.jikanKbnDefault === "undefined") return;
      setValue("jikanKbn", defaultSelectedTime.jikanKbnDefault);
    }
  }, [
    defaultValue?.getApiReceptionGetDefaultSelectedTime?.data?.data,
    patientDetail?.birthday,
    setValue,
  ]);

  useEffect(() => {
    const defaultPrescription =
      defaultValue?.getApiReceptionGetDefaultPrescription?.data;
    const { prescriptionIssueType, prinEpsReference } =
      defaultPrescription || {};
    let value = "2-2";
    if (prescriptionIssueType === 1) {
      value = prinEpsReference === 2 ? "1-2" : "1-1";
    }
    setValue("prescription_type", value);
  }, [defaultValue?.getApiReceptionGetDefaultPrescription?.data, setValue]);

  useEffect(() => {
    if (isEdit && receptionDetail) {
      if (
        listTreatmentOptions.some(
          (item) => item.value === receptionDetail.treatmentDepartmentId,
        )
      ) {
        setValue("treatmentMenu", receptionDetail.treatmentDepartmentId);
      }

      setValue("receptionComment", receptionDetail.comment ?? "");
    }
  }, [isEdit, listTreatmentOptions, receptionDetail, setValue]);

  useEffect(() => {
    if (!hasPrescriptionSection) {
      setValue("prescription_type", "2-2");
    }
  }, [setValue, hasPrescriptionSection]);

  return (
    <>
      {isSubmitting && <ModalLoading />}
      <FormProvider {...methods}>
        <Form id="add-reception" onSubmit={handleSubmit(onSubmit)}>
          <Section>
            <SectionTitle>受付情報</SectionTitle>
            <InputArea>
              <InputWrapper>
                <StyledInputLabel required label="診療メニュー" />
                <Controller
                  name="treatmentMenu"
                  control={control}
                  rules={{
                    validate: (value) => {
                      if (value === 0) return true;
                      if (!value || value === -1) {
                        return "診療メニューを入力してください。";
                      }
                      return true;
                    },
                  }}
                  render={({ field, fieldState }) => (
                    <div>
                      <StyledSelect
                        options={listTreatmentOptions}
                        status={fieldState.error ? "error" : ""}
                        width="full"
                        placeholder="選択してください"
                        {...field}
                        value={field.value || undefined}
                      />
                      <RenderIf condition={!!fieldState.error}>
                        <StyledErrorText>
                          {fieldState.error?.message}
                        </StyledErrorText>
                      </RenderIf>
                    </div>
                  )}
                />
              </InputWrapper>
            </InputArea>
            <InputArea>
              <Flex gap={20}>
                <InputWrapper>
                  <StyledInputLabel required label="担当医師" />
                  <Controller
                    name="doctorId"
                    control={control}
                    rules={{
                      validate: (value) => {
                        if (value === 0) return true;
                        if (!value || value === -1) {
                          return "担当医師を入力してください。";
                        }
                        return true;
                      },
                    }}
                    render={({ field, fieldState }) => (
                      <div>
                        <StyledSelect
                          options={listDoctors}
                          placeholder="選択してください"
                          status={fieldState.error ? "error" : ""}
                          width={200}
                          {...field}
                        />
                        <RenderIf condition={!!fieldState.error}>
                          <StyledErrorText>
                            {fieldState.error?.message}
                          </StyledErrorText>
                        </RenderIf>
                      </div>
                    )}
                  />
                </InputWrapper>
                <InputWrapper>
                  <StyledInputLabel label="初再診" />
                  <Controller
                    name="syosaisinKbn"
                    control={control}
                    render={({ field }) => (
                      <StyledSelect
                        disabled={
                          isEdit && !!receptionDetail?.karteEditionRaiinNo
                        }
                        options={FIRST_VISIT}
                        placeholder="選択してください"
                        width={200}
                        {...field}
                      />
                    )}
                  />
                </InputWrapper>
                <InputWrapper>
                  <StyledInputLabel required label="時間帯" />
                  <Controller
                    name="jikanKbn"
                    disabled={isEdit && !!receptionDetail?.karteEditionRaiinNo}
                    control={control}
                    rules={{
                      validate: (value) => {
                        if (value === 0) return true;
                        if (!value || value === -1) {
                          return "時間帯を入力してください。";
                        }
                        return true;
                      },
                    }}
                    render={({ field, fieldState }) => (
                      <div>
                        <StyledSelect
                          options={timeZoneList}
                          width={200}
                          status={fieldState.error ? "error" : ""}
                          {...field}
                        />
                        <RenderIf condition={!!fieldState.error}>
                          <StyledErrorText>
                            {fieldState.error?.message}
                          </StyledErrorText>
                        </RenderIf>
                      </div>
                    )}
                  />
                </InputWrapper>
              </Flex>
            </InputArea>
            <RenderIf condition={hasPrescriptionSection}>
              <InputArea>
                <InputWrapper>
                  <StyledInputLabel label="処方箋" />
                  <Controller
                    name="prescription_type"
                    control={control}
                    render={({ field }) => (
                      <StyledSelect
                        placeholder="選択してください"
                        width={200}
                        {...field}
                      >
                        {Object.entries(PRESCRIPTION_TYPE).map(
                          ([value, label]) => (
                            <Select.Option key={value} value={value}>
                              {label}
                            </Select.Option>
                          ),
                        )}
                      </StyledSelect>
                    )}
                  />
                </InputWrapper>
              </InputArea>
            </RenderIf>
            <InputArea>
              <Flex vertical>
                <Flex align="flex-end">
                  <InputWrapper>
                    <StyledInputLabel label="当日メモ" />
                    <Controller
                      name="receptionComment"
                      control={control}
                      rules={{
                        maxLength: {
                          value: 500,
                          message: "500文字以内で入力してください。",
                        },
                      }}
                      render={({ field, fieldState }) => (
                        <div>
                          <StyledInput
                            hasError={!!fieldState.error}
                            width={668}
                            {...field}
                          />
                          {fieldState.error?.message && (
                            <StyledErrorText>
                              {fieldState.error?.message}
                            </StyledErrorText>
                          )}
                        </div>
                      )}
                    />
                  </InputWrapper>
                  <LabelsPopover
                    receptionDetail={receptionDetail}
                    isEdit={isEdit}
                  />
                </Flex>
                <LabelsWrapper>
                  {!!labelList &&
                    labelList
                      ?.filter((label) => label.checked)
                      .map((label) => (
                        <StyledLabels
                          key={`${label.grpId}-${label.hpId}`}
                          $color={
                            label.colorCd?.startsWith("#")
                              ? label.colorCd
                              : `#${label.colorCd}`
                          }
                        >
                          <span className="dots-item">&nbsp;</span>
                          {label.grpName ?? ""}
                        </StyledLabels>
                      ))}
                </LabelsWrapper>
              </Flex>
            </InputArea>
          </Section>
          <Section>
            <Flex gap={20}>
              <SectionTitle>患者情報</SectionTitle>
              <RenderIf condition={isLinkCard}>
                <div>
                  <Controller
                    render={({ field }) => (
                      <Checkbox checked={field.value} onChange={field.onChange}>
                        診察券を印刷
                      </Checkbox>
                    )}
                    control={control}
                    name={"printMedicalCardChecked"}
                  />
                </div>
              </RenderIf>
            </Flex>
            <InputArea justify="space-between">
              <InputWrapper>
                <StyledLabel>氏名</StyledLabel>
                <StyledContentInfor>{patientDetail?.name}</StyledContentInfor>
              </InputWrapper>
              <InputWrapper>
                <StyledLabel>氏名（カナ）</StyledLabel>
                <StyledContentInfor>
                  {patientDetail?.kanaName}
                </StyledContentInfor>
              </InputWrapper>
              <InputWrapper>
                <StyledLabel>生年月日</StyledLabel>
                <StyledContentInfor>{ptBirthday}</StyledContentInfor>
              </InputWrapper>
            </InputArea>
          </Section>
          <Section>
            <SectionTitle>保険組み合わせ履歴</SectionTitle>
            <InputArea>
              <InputWrapper>
                <Controller
                  name="hokenPattern.hokenPid"
                  control={control}
                  render={({ field }) => (
                    <StyledSelect
                      placeholder="選択してください"
                      width="full"
                      options={hokenPatientList}
                      {...field}
                      error={isSomeInsuranceInHokenPatternExpired(
                        insurancesData?.listHokenPattern?.find(
                          (item) => item.hokenPid === field.value,
                        ),
                      )}
                      onChange={(value) => {
                        field.onChange(value);
                        const hokenPattern = getHokenPatternReceptionForm(
                          insurancesData?.listHokenPattern?.find(
                            (item) => item.hokenPid === value,
                          ),
                        );
                        refHokenPattern.current = { ...hokenPattern };

                        setValue("hokenPattern", hokenPattern);
                        refKohi?.current?.resetKohiList(hokenPattern);
                        if (isSubmitted) {
                          trigger("hokenPattern.hokenId");
                          trigger("hokenPattern.kohi1Id");
                        }
                      }}
                    />
                  )}
                />
              </InputWrapper>
            </InputArea>
          </Section>
          <HokenInfo
            insurancesData={insurancesData}
            patientDetail={patientDetail}
            resetKohiList={refKohi.current?.resetKohiList}
            setClickConfirmDate={() => setClickConfirmDate(true)}
          />
          <RenderIf condition={!isJihi100Percent}>
            <KohiInfo
              insurancesData={insurancesData}
              ref={refKohi}
              patientDetail={patientDetail}
              selectedSinDate={selectedSinDate}
              setClickConfirmDate={() => setClickConfirmDate(true)}
            />
          </RenderIf>
          <RenderIf condition={!!lastComeDate && !!firstTimeConfirmDays}>
            <FirstVisitFeeModal
              lastComeDate={lastComeDate}
              firstTimeConfirmDays={firstTimeConfirmDays}
              onClickFirstTimeVisit={() => {
                setValue("syosaisinKbn", FIRST_VISIT_VALUES.FIRST_VISIT);
                setLastComeDate(undefined);
                setFirstTimeConfirmDays(undefined);
              }}
              onClose={() => {
                setLastComeDate(undefined);
                setFirstTimeConfirmDays(undefined);
              }}
            />
          </RenderIf>
        </Form>
      </FormProvider>
      <RenderIf condition={hokenPatternValidation[0]?.typeMessage === 4}>
        <HokenPatternConfirmModal
          open={!!hokenPatternValidation[0]}
          onClose={() => {
            setDisabledButtonAdd?.(false);
            setHokenPatternValidation([]);
          }}
          content={hokenPatternValidation[0] ?? {}}
          onConfirm={handleConfirmCreateHokenPattern}
        />
      </RenderIf>
      <RenderIf condition={hokenPatternValidation[0]?.typeMessage === 2}>
        <HokenPatternErrorModal
          open={!!hokenPatternValidation[0]}
          onClose={() => {
            setHokenPatternValidation([]);
            setDisabledButtonAdd?.(false);
          }}
          content={hokenPatternValidation[0] ?? {}}
        />
      </RenderIf>
    </>
  );
};

const LabelsWrapper = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 6px;
`;

const Section = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e3e5;

  &:last-of-type {
    border-bottom: none;
  }
`;

const SectionTitle = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  margin-bottom: 16px;
`;

const InputArea = styled(Flex)`
  gap: 12px;
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const InputWrapper = styled.div`
  width: 100%;
`;

const StyledInputLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledLabel = styled.p`
  font-size: 14px;
  line-height: 14px;
  margin-bottom: 4px;
  color: #6a757d;
`;

const StyledSelect = styled(Pulldown).withConfig({
  shouldForwardProp: (prop) => prop !== "error",
})<{
  width: string | number;
  error?: boolean;
}>(({ width, error }) => ({
  width: width === "full" ? "100%" : `${width}px`,

  ...(error && {
    ".ant-select-selection-item": {
      color: "#e74c3c",
    },
  }),
}));

const StyledInput = styled(TextInput)<{ width: string | number }>`
  width: ${(props) => (props.width === "full" ? "100%" : `${props.width}px`)};
  height: 32px;
`;

const StyledLabels = styled.p<{ $color?: string }>`
  font-size: 11px;
  line-height: 12px;
  display: flex;
  justify-content: center;
  align-items: center;

  > .dots-item {
    width: 10px;
    height: 10px;
    display: inline-block;
    margin-right: 3px;
    border-radius: 50%;
    ${({ $color }) =>
      `background-color: ${$color || "#4ebbe0"};
    `}
  }
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 8px;
`;

const StyledContentInfor = styled.p`
  font-size: 16px;
  font-weight: normal;
  text-align: left;
  color: #243544;
  max-width: calc(760px / 3);
`;
