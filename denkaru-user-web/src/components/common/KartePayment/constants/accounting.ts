export enum AccountingCheckingStatus {
  Succeed = 1,
  Failed = 2,
  NoData = 3,
  StateChanged = 4,
  VisitRemoved = 5,
  BillUpdated = 6,
  ValidPaymentAmount = 7,
  ValidThisCredit = 8,
  DateNotVerify = 9,
}

// NyukinKbn: 0:未精算 1:一部精算 2:免除 3:精算済
export enum NYUKINKBN {
  UNSETTLED = 0,
  PARTIALLY = 1,
  EXEMPT = 2,
  SETTLED = 3,
}

export const IsIncludeOutDrugKey = "ACCOUNTING_IS_INCLUDE_OUT_DRUG";

import type { RaiinNoSelection } from "../types/accounting-detail";

export const ItemCdConst = {
  TouyakuChozaiNaiTon: "*********",
  TouyakuChozaiGai: "*********",
  ConTouyakuOrSiBunkatu: "@BUNKATU",
  Comment820Pattern: "820",
  Comment830Pattern: "830",
  Comment831Pattern: "831",
  Comment840Pattern: "840",
  Comment842Pattern: "842",
  Comment850Pattern: "850",
  Comment851Pattern: "851",
  Comment852Pattern: "852",
  Comment853Pattern: "853",
  Comment880Pattern: "880",
  GazoDensibaitaiHozon: "*********",
} as const;

export const RaiinNoAllSelection: RaiinNoSelection = {
  raiinNo: "-1",
  raiinBinding: "(すべて)",
  patternName: "",
};

export const PaymentMethodCd = {
  Cash: 0,
  Credit: 1,
  Debit: 2,
  Transfer: 3,
  ElectronicMoney: 4,
  OnlinePayment: 5,
  FCO: 6,
} as const;

export const EnTenKbn = {
  Ten: 0,
  Yen: 1,
} as const;

export const PrintType = {
  Receipt: 0,
  Detail: 1,
  ReceiptAndDetail: 4,
} as const;

export const NotFoundPrintText = "印刷対象が見つかりません";
