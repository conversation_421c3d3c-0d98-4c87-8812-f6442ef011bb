import { useEffect, useMemo, useRef, useState } from "react";

import dayjs from "dayjs";

import { usePostApiAccountDueSaveListMutation } from "@/apis/gql/operations/__generated__/payment";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useSession } from "@/hooks/useSession";
import { useRenkei } from "@/hooks/useRenkei";
import { RenkeiCode } from "@/constants/renkei";

import { useModal } from "../providers/ModalProvider";
import { standardizeAccountDueSaveList } from "../utils/payment";
import { NYUKINKBN, PaymentMethodCd } from "../constants/accounting";
import { useAccountingQuery } from "../providers/AccountingQueryProvider";

import type { AccountDueList, ObjectType } from "../types/accounting-detail";
import type { EmrCloudApiRequestsAccountDueAccountDueItemInput } from "@/apis/gql/generated/types";

export const usePaymentListPaidEditModal = (
  accountDueList: AccountDueList,
  raiinNo: string,
) => {
  const { state: modal, handleCloseModal, handleOpenModal } = useModal();
  const [paymentListTemp, setPaymentListTemp] = useState<
    EmrCloudApiRequestsAccountDueAccountDueItemInput[]
  >([]);
  const deletedRow = useRef<string[]>([]);
  const changedRow = useRef<string[]>([]);
  const { handleError } = useErrorHandler();
  const [accountDueSaveList, { loading }] =
    usePostApiAccountDueSaveListMutation({
      refetchQueries: ["getApiAccountDueGetList"],
      onError: (error) => handleError({ error }),
    });
  const { notification } = useGlobalNotification();
  const { ptId } = useAccountingQuery();
  const { session } = useSession();
  const { sendRenkei } = useRenkei();

  const paymentMethods = accountDueList?.listPaymentMethod ?? {};
  const payments = accountDueList?.accountDueList ?? [];
  const targetPayments = payments.filter(
    (payment) => payment.raiinNo === raiinNo,
  );
  const paymentOptions = Object.keys(paymentMethods).map((item) => ({
    value: Number(item),
    label: paymentMethods[item],
  }));
  const seikyuGaku = targetPayments[0]?.seikyuGaku ?? 0;

  const addRow = () => {
    const nyukinGaku = seikyuGaku - paymentValue;

    if (!nyukinGaku && paymentListTemp.find((ele) => !ele.isDelete)) {
      notification.error({ message: "未収金がないため、実行できません。" });
      return;
    }

    setPaymentListTemp([
      ...paymentListTemp,
      {
        adjustFutan: 0,
        isDelete: false,
        isUpdated: true,
        nyukinCmt: "",
        nyukinKbn: NYUKINKBN.SETTLED,
        raiinInfStatus: targetPayments[0]?.raiinInfStatus,
        raiinNo: targetPayments[0]?.raiinNo,
        seikyuAdjustFutan: targetPayments[0]?.seikyuAdjustFutan,
        seikyuDetail: targetPayments[0]?.seikyuDetail,
        seikyuGaku: targetPayments[0]?.seikyuGaku,
        seikyuSinDate: targetPayments[0]?.seikyuSinDate,
        seikyuTensu: targetPayments[0]?.seikyuTensu,
        seqNo: "0",
        sortNo: targetPayments[0]?.sortNo,
        uketukeSbt: targetPayments[0]?.uketukeSbt,
        nyukinGaku,
        paymentMethodCd: paymentOptions[0]?.value,
        nyukinDate: Number(dayjs().format("YYYYMMDD")),
      },
    ]);
  };

  const removeRow = (idx: number, seqNo?: string) => {
    const clone = [...paymentListTemp];
    clone[idx] = { ...clone[idx], isDelete: true };
    setPaymentListTemp(clone);
    if (seqNo !== undefined && seqNo !== "0") deletedRow.current.push(seqNo);
  };

  const setPaymentListTempEle = (item: ObjectType, position: number) => {
    const clone = [...paymentListTemp];
    clone[position] = { ...clone[position], ...item };
    setPaymentListTemp(clone);
  };

  useEffect(() => {
    if (!modal.paymentListPaidEditOpen || !raiinNo) {
      setPaymentListTemp([]);
      deletedRow.current = [];
      changedRow.current = [];
      return;
    }

    const payments = accountDueList.accountDueList ?? [];
    const targetPayments = payments.filter(
      (payment) => Number(payment.raiinNo) === Number(raiinNo),
    );

    if (
      targetPayments.length === 1 &&
      targetPayments[0]?.nyukinKbn === NYUKINKBN.UNSETTLED &&
      targetPayments[0]?.paymentMethodCd !== PaymentMethodCd.FCO
    ) {
      setPaymentListTemp([]);
      return;
    }

    setPaymentListTemp(targetPayments);
    // eslint-disable-next-line
  }, [modal.paymentListPaidEditOpen]);

  const isValidNewPayment = (
    payment: EmrCloudApiRequestsAccountDueAccountDueItemInput,
  ) => {
    if (payment.seqNo !== "0" || payment.isDelete) return false;
    if (payment.seikyuGaku === 0) return true;
    return payment.nyukinGaku || payment.adjustFutan || payment.nyukinCmt;
  };

  const prepareSaveList = () => {
    const newPayments = [...paymentListTemp].filter(isValidNewPayment);
    if (newPayments.length) {
      const cleanedData = [...paymentListTemp].filter((payment) => {
        if (payment.seqNo === "0") return isValidNewPayment(payment);

        const isDeleteRow = deletedRow.current.includes(
          payment.seqNo as string,
        );
        payment = { ...payment, isDelete: isDeleteRow };

        return true;
      });

      return cleanedData.map((item, idx) => ({
        ...item,
        sortNo: idx + 1,
      }));
    }

    if (!changedRow.current.length && !deletedRow.current.length) return [];

    const list: EmrCloudApiRequestsAccountDueAccountDueItemInput[] = [];
    deletedRow.current.forEach((seqNo) => {
      const target = targetPayments.find((item) => item.seqNo === seqNo);
      list.push({ ...target, isDelete: true });
    });
    changedRow.current.forEach((seqNo) => {
      const rawTarget = targetPayments.find(
        (payment) => payment.seqNo === seqNo,
      );
      const changedTarget = paymentListTemp.find(
        (payment) => payment.seqNo === seqNo,
      );
      if (
        JSON.stringify(rawTarget) !== JSON.stringify(changedTarget) &&
        changedTarget
      ) {
        list.push(changedTarget);
      }
    });

    return list;
  };

  const save = async () => {
    const listAccountDues = prepareSaveList();
    const hasFCOPayment = listAccountDues.some(
      (item) =>
        item.paymentMethodCd === PaymentMethodCd.FCO &&
        item.seqNo &&
        changedRow.current.includes(item.seqNo),
    );

    if (hasFCOPayment) {
      handleError({
        error: new Error("FCO精算はできません。"),
        commonMessage: "FCO精算はできません。",
      });

      return;
    }

    changedRow.current = [];

    if (!listAccountDues.length) {
      handleCloseModal("PAYMENT_LIST_PAID_EDIT");
      return;
    }

    const hasNonDeletedItems = paymentListTemp.some((item) => !item.isDelete);
    const nyukinKbn =
      paymentValue === seikyuGaku && hasNonDeletedItems
        ? NYUKINKBN.SETTLED
        : paymentValue === 0
          ? hasNonDeletedItems
            ? NYUKINKBN.PARTIALLY
            : NYUKINKBN.UNSETTLED
          : NYUKINKBN.PARTIALLY;

    await accountDueSaveList({
      variables: {
        input: {
          ptId,
          sinDate: Number(dayjs().format("YYYYMMDD")),
          userId: session.staffInfo?.staffId,
          listAccountDues: standardizeAccountDueSaveList(listAccountDues, {
            nyukinKbn,
          }),
          kaikeiTime: dayjs().format("HHmmss").toString() ?? "",
        },
      },
      onCompleted: () => {
        sendRenkei({
          raiinNo: Number(raiinNo ?? 0),
          sinDate: Number(
            targetPayments[0]?.sinDateDisplay?.replace(/\//g, "") ?? 0,
          ),
          ptId: Number(ptId),
          eventCd: RenkeiCode.AccountingUpdate,
          // nyukin: nyukinKbn,
        });
        handleCloseModal("PAYMENT_LIST_PAID_EDIT");
        handleOpenModal("PAYMENT_LIST_COMPLETE");
      },
    });
  };

  const paymentValue = useMemo(() => {
    return paymentListTemp.reduce((sum, item) => {
      const nyukinGaku = item.isDelete ? 0 : (item?.nyukinGaku ?? 0);
      const adjustFutan = item.isDelete ? 0 : (item?.adjustFutan ?? 0);

      return sum + nyukinGaku + adjustFutan;
    }, 0);
  }, [paymentListTemp]);

  const saveChangedRow = (idx: number) => {
    const tagetSeqNo = paymentListTemp[idx]?.seqNo as string;

    if (
      paymentListTemp[idx]?.seqNo === "0" ||
      changedRow.current.includes(tagetSeqNo)
    ) {
      return;
    }

    changedRow.current.push(tagetSeqNo);
  };

  const handleDatePickerValue = (nyukinDate?: number) => {
    return dayjs(
      nyukinDate
        ? `${nyukinDate}`.replace(/(\d{4})(\d{2})(\d{2})/g, "$1-$2-$3")
        : undefined,
    );
  };

  const seikyuSinDate = dayjs(
    targetPayments[0]?.seikyuSinDate + "",
    "YYYYMMDD",
  ).format("YYYY/MM/DD");

  const handlePaidValue = (
    paymentList: EmrCloudApiRequestsAccountDueAccountDueItemInput[],
    idx: number,
  ) => {
    return paymentList.reduce((acc, payment, currentIdx) => {
      if (payment.isDelete || currentIdx > idx) return acc;

      return acc + (payment.adjustFutan || 0) + (payment.nyukinGaku || 0);
    }, 0);
  };

  return {
    addRow,
    setPaymentListTempEle,
    saveChangedRow,
    removeRow,
    paymentListTemp,
    save,
    seikyuGaku,
    targetPayments,
    paymentOptions,
    handleDatePickerValue,
    seikyuSinDate,
    handlePaidValue,
    saveLoading: loading,
  };
};
