import { useMemo } from "react";

import dayjs from "dayjs";
import { useRouter } from "next/router";

import { usePostApiAccountDueSaveListMutation } from "@/apis/gql/operations/__generated__/payment";
import {
  NYUKINKBN,
  PaymentMethodCd,
} from "@/components/common/KartePayment/constants/accounting";
import { useTreatmentDepartmentListQuery } from "@/hooks/add-patient/useTreatmentDepartmentListQuery";
import { useSession } from "@/hooks/useSession";
import { usePutApiVisitingUpdateStaticCellMutation } from "@/apis/gql/operations/__generated__/reception";
import { RaiinInfStatus } from "@/constants/reservation";

import { useErrorHandler } from "../../../../hooks/useErrorHandler";
import { useAccountingQuery } from "../providers/AccountingQueryProvider";
import { useModal } from "../providers/ModalProvider";
import { findNotDuplicateAccountDue } from "../utils/accounting-detail";
import { standardizeAccountDueSaveList } from "../utils/payment";

import type { EmrCloudApiResponsesAccountDueAccountDueDto } from "@/apis/gql/generated/types";
import type { AccountDueList } from "../types/accounting-detail";

type Props = {
  accountDueList: AccountDueList;
  filterUnpaid: boolean;
  filterUncalculate: boolean;
};

export const usePaymentListTable = ({
  accountDueList,
  filterUnpaid,
  filterUncalculate,
}: Props) => {
  const payments = accountDueList?.accountDueList ?? [];
  const paymentMethods = accountDueList?.listPaymentMethod ?? {};
  const fcoLabel = accountDueList?.fcoLabel ?? "";

  const { push } = useRouter();

  const { session } = useSession();

  const { handleCloseModal } = useModal();
  const { handleError } = useErrorHandler();

  const [accountDueSaveList, { loading }] =
    usePostApiAccountDueSaveListMutation({
      refetchQueries: ["getApiAccountDueGetList"],
      onError: (error) => handleError({ error }),
    });
  const { ptId } = useAccountingQuery();
  const { listMenuTreatment } = useTreatmentDepartmentListQuery();

  const [updateItemCell] = usePutApiVisitingUpdateStaticCellMutation({
    onError: (error) => {
      handleError({ error });
    },
  });

  const defaultSaveListInput = {
    ptId,
    sinDate: Number(dayjs().format("YYYYMMDD")),
    userId: session.staffInfo?.staffId,
  };

  const paidList = useMemo(() => {
    const uniqueRaiinNo = new Set();
    const uniqueAccountDue: (EmrCloudApiResponsesAccountDueAccountDueDto & {
      key: string;
    })[] = [];

    payments.forEach((payment) => {
      if (!uniqueRaiinNo.has(payment.raiinNo)) {
        uniqueRaiinNo.add(payment.raiinNo);
        uniqueAccountDue.push({ ...payment, key: payment.seqNo! });
      }
    });

    return (
      uniqueAccountDue
        .filter((payment) => {
          if (filterUnpaid && filterUncalculate) {
            return payment.nyukinKbn === NYUKINKBN.PARTIALLY;
          }

          if (filterUnpaid) {
            return [NYUKINKBN.PARTIALLY, NYUKINKBN.UNSETTLED].includes(
              payment.nyukinKbn!,
            );
          }

          return true;
        })
        // 変更された会計が最初に移動
        .sort((a, b) => {
          const aChanged = a.newSeikyuGaku !== a.seikyuGaku;
          const bChanged = b.newSeikyuGaku !== b.seikyuGaku;

          return aChanged === bChanged ? 0 : aChanged ? -1 : 1;
        })
    );
    // eslint-disable-next-line
  }, [accountDueList, filterUnpaid, filterUncalculate]);

  const isDuplicateAccountDue = (raiinNo: number) =>
    !findNotDuplicateAccountDue(payments).includes(raiinNo.toString());

  const exempt = async (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ) => {
    const isUnSettled = payment.nyukinKbn === NYUKINKBN.UNSETTLED;

    if (isFcoPayment(payment) || !isUnSettled) return;

    await accountDueSaveList({
      variables: {
        input: {
          ...defaultSaveListInput,
          listAccountDues: standardizeAccountDueSaveList([payment], {
            nyukinKbn: NYUKINKBN.EXEMPT,
          }),
          kaikeiTime: dayjs().format("HHmmss").toString() ?? "",
        },
      },
    });
  };

  const deletePayment = async (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ) => {
    await accountDueSaveList({
      variables: {
        input: {
          ...defaultSaveListInput,
          listAccountDues: standardizeAccountDueSaveList([payment], {
            nyukinKbn: handleNyukinKbnInDeletePayment(payment),
            isDelete: true,
          }),
          kaikeiTime: dayjs().format("HHmmss").toString() ?? "",
        },
      },
      onCompleted: () => {
        updateItemCell({
          variables: {
            ptId,
            cellName: "Status",
            cellValue: `${RaiinInfStatus.AMOUNT_FIXED}`,
            raiinNo: payment.raiinNo,
            sinDate: payment.seikyuSinDate,
          },
        });
      },
    });
  };

  const handleNyukinKbnInDeletePayment = (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ): NYUKINKBN => {
    const paymentQuantity = accountDueList.accountDueList!.filter(
      (ele) => ele.raiinNo === payment.raiinNo,
    ).length;
    const isFCO = payment.paymentMethodCd === PaymentMethodCd.FCO;
    const isSettled = payment.nyukinKbn === NYUKINKBN.SETTLED;
    const isZeroPayment =
      payment.seikyuGaku === 0 &&
      payment.adjustFutan === 0 &&
      payment.nyukinGaku === 0;
    const isZeroGakuAndSettled = isSettled && isZeroPayment;

    if (paymentQuantity === 1 && (isFCO || isZeroGakuAndSettled))
      return NYUKINKBN.UNSETTLED;

    return isSettled && payment.nyukinGaku === 0 && payment.adjustFutan === 0
      ? NYUKINKBN.SETTLED
      : paymentQuantity >= 2 &&
          [NYUKINKBN.SETTLED, NYUKINKBN.PARTIALLY].includes(payment.nyukinKbn!)
        ? NYUKINKBN.PARTIALLY
        : NYUKINKBN.UNSETTLED;
  };

  const redirectToPayment = (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ) => {
    if (!payment.ptId) return;

    handleCloseModal("PAYMENT_LIST");
    push({
      pathname: `/karte/${payment.ptId}`,
      query: {
        sinDate: payment.seikyuSinDate,
        raiinNo: payment.raiinNo,
        openPayment: true,
      },
    });
  };

  const handleExpandRowCondition = (raiinNo: number) => {
    return (
      isDuplicateAccountDue(raiinNo) ||
      !!accountDueList.accountDueList?.filter((payment) => {
        return Number(payment.raiinNo) === raiinNo && payment.nyukinCmt;
      }).length
    );
  };

  const getTreatmentDepartmentTitle = (treatmentDepartmentId: number) =>
    listMenuTreatment.find(
      (item) => item.treatmentDepartmentId === treatmentDepartmentId,
    )?.title ?? "";

  const handleShowPaymentMethod = (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ) => {
    const { paymentMethodCd, nyukinKbn } = payment;
    const numberNyukinKbn = Number(nyukinKbn);

    if (
      numberNyukinKbn === NYUKINKBN.EXEMPT ||
      (numberNyukinKbn === NYUKINKBN.UNSETTLED &&
        paymentMethodCd !== PaymentMethodCd.FCO)
    )
      return "";

    return paymentMethodCd === PaymentMethodCd.FCO
      ? fcoLabel
      : paymentMethods[`${paymentMethodCd}`];
  };

  const isFcoPayment = (
    payment: EmrCloudApiResponsesAccountDueAccountDueDto,
  ) => {
    return payment.paymentMethodCd === PaymentMethodCd.FCO;
  };

  return {
    paidList,
    deletePayment,
    exempt,
    isDuplicateAccountDue,
    redirectToPayment,
    handleExpandRowCondition,
    getTreatmentDepartmentTitle,
    handleShowPaymentMethod,
    savePaymentLoading: loading,
    isFcoPayment,
  };
};
