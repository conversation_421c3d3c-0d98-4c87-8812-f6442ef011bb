import { useState } from "react";

import { ApolloError } from "@apollo/client";

import {
  useGetApiPdfCreatorExportDrugInfoLazyQuery,
  useGetApiPdfCreatorOutDrugLazyQuery,
} from "@/apis/gql/operations/__generated__/print-setting";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";

import {
  generatePdfDocument,
  getDrgLabel,
  getDrugNote,
  getReceiptPreview,
  getReceiptReport,
} from "../utils/accounting-detail";
import { NotFoundPrintText, PrintType } from "../constants/accounting";

import { useCheckOpenFormReceiptReport } from "./useCheckOpenFormReceiptReport";
import { useCheckOrderInfoInDrug } from "./useCheckOrderInfoInDrug";
import { useSystemConfig } from "./useSystemConfig";

import type {
  DomainModelsAccountDueSyunoSeikyuModel,
  UseCaseAccountingGetAccountingSystemConfAccountingConfigDto,
} from "@/apis/gql/generated/types";

export const usePrint = () => {
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const { handleError } = useErrorHandler();

  const { handleCheckOpenFormReceiptReport } = useCheckOpenFormReceiptReport();

  const { handleOrderInfoInDrug } = useCheckOrderInfoInDrug();

  const [getOutDrug] = useGetApiPdfCreatorOutDrugLazyQuery();

  const [getDrugInfo] = useGetApiPdfCreatorExportDrugInfoLazyQuery();

  const { ShouldPrint0YenReceipt, ShouldPrint0YenReceiptDetail } =
    useSystemConfig();

  const handleGenerateReceiptReport = async (
    ptId: string,
    raiinNoList: string[],
    raiinNoPayList: string[],
    printConfig: UseCaseAccountingGetAccountingSystemConfAccountingConfigDto,
    data: DomainModelsAccountDueSyunoSeikyuModel[],
    isCalculateProcess: boolean = false,
  ) => {
    for (const seikyu of data) {
      if (seikyu.newSeikyuGaku !== seikyu.seikyuGaku) {
        handleError({
          error: new Error(
            "請求金額が変更されているため、領収証を印刷できません。\n 収納一覧を確認し再実行してください。",
          ),
          commonMessage:
            "請求金額が変更されているため、領収証を印刷できません。\n 収納一覧を確認し再実行してください。",
        });

        return;
      }
    }

    const printType =
      printConfig.isCheckedPrintReceipt && printConfig.isCheckedPrintDetail
        ? PrintType.ReceiptAndDetail
        : printConfig.isCheckedPrintReceipt
          ? PrintType.Receipt
          : printConfig.isCheckedPrintDetail
            ? PrintType.Detail
            : undefined;

    if (printType === undefined) {
      return;
    }

    if (
      isCalculateProcess &&
      !ShouldPrint0YenReceipt() &&
      (printType === PrintType.Receipt ||
        printType === PrintType.ReceiptAndDetail) &&
      raiinNoPayList.length === 0
    ) {
      return;
    }

    if (
      isCalculateProcess &&
      !ShouldPrint0YenReceiptDetail() &&
      (printType === PrintType.Detail ||
        printType === PrintType.ReceiptAndDetail) &&
      raiinNoPayList.length === 0
    ) {
      return;
    }

    const isValid = await handleCheckOpenFormReceiptReport(
      ptId,
      printType,
      raiinNoList,
      raiinNoPayList,
      isCalculateProcess,
    );

    if (!isValid) {
      handleError({
        error: new Error("領収証の出力に失敗しました。"),
        commonMessage: "領収証の出力に失敗しました。",
      });

      return;
    }

    try {
      const response = await getReceiptReport({
        printType,
        raiinNoList,
        raiinNoPayList,
        isCalculateProcess,
        ptId,
      });

      await generatePdfDocument(response);
    } catch (error) {
      logger({ error, message: "failed to generate receipt report" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error, commonMessage: "領収証の出力に失敗しました。" });
      }
    }
  };

  const handleGenerateOutDrug = async (
    ptId: string,
    sinDate: number,
    raiinNos: string[],
  ) => {
    const requests = raiinNos.map(async (raiinNo) => {
      try {
        const response = await getOutDrug({
          variables: {
            ptId,
            sinDate,
            raiinNo,
            epsPrintType: 1,
            hokenGp: -1,
          },
        });

        if (response.data?.getApiPdfCreatorOutDrug) {
          const { fileUrl, message, status } =
            response.data.getApiPdfCreatorOutDrug;

          if (status !== 0) {
            // 印刷対象が見つからない場合は、エラーを表示しない
            if (message && message.includes(NotFoundPrintText)) {
              return;
            }

            await generatePdfDocument(fileUrl || "", message);
            return;
          }

          if (fileUrl) {
            await generatePdfDocument(fileUrl);
          }
        }
      } catch (error) {
        logger({
          error,
          message: `failed to generate out drug, raiinNo: ${raiinNo}`,
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "院外処方箋の出力に失敗しました。",
          });
        }
      }
    });

    await Promise.all(requests);
  };

  const handleGenerateDrgLabel = async (
    ptId: string,
    sinDate: number,
    raiinNos: string[],
  ) => {
    const requests = raiinNos.map(async (raiinNo) => {
      try {
        const response = await getDrgLabel({
          ptId,
          sinDate,
          raiinNo,
        });

        // 印刷対象が見つからない場合は、エラーを表示しない
        if (response?.headers["content-type"] === "text/html") {
          const text = await response?.data?.text();

          if (text?.includes(NotFoundPrintText)) {
            return;
          }
        }

        await generatePdfDocument(response);
      } catch (error) {
        logger({
          error,
          message: `failed to generate drug label, raiinNo: ${raiinNo}`,
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "薬袋ラベルの出力に失敗しました。",
          });
        }
      }
    });

    await Promise.all(requests);
  };

  const handleGenerateDrgInf = async (
    ptId: string,
    sinDate: number,
    raiinNos: string[],
  ) => {
    const requests = raiinNos.map(async (raiinNo) => {
      try {
        const isValid = await handleOrderInfoInDrug(ptId, raiinNo);

        if (!isValid) {
          handleError({
            error: new Error("薬情の出力に失敗しました。"),
            commonMessage: "薬情の出力に失敗しました。",
          });

          return;
        }

        const response = await getDrugInfo({
          variables: {
            ptId,
            sinDate,
            raiinNo,
          },
        });

        if (response.data?.getApiPdfCreatorExportDrugInfo) {
          const { fileUrl, message, status } =
            response.data.getApiPdfCreatorExportDrugInfo;

          if (status !== 0) {
            // 印刷対象が見つからない場合は、エラーを表示しない
            if (message && message.includes(NotFoundPrintText)) {
              return;
            }

            await generatePdfDocument(fileUrl || "", message);
            return;
          }

          if (fileUrl) {
            await generatePdfDocument(fileUrl);
          }
        }
      } catch (error) {
        logger({
          error,
          message: `failed to generate drug inf, raiinNo: ${raiinNo}`,
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "薬情の出力に失敗しました。",
          });
        }
      }
    });

    await Promise.all(requests);
  };

  const handleGenerateDrugNote = async (
    ptId: string,
    sinDate: number,
    raiinNos: string[],
  ) => {
    const requests = raiinNos.map(async (raiinNo) => {
      try {
        const response = await getDrugNote({
          ptId,
          sinDate,
          raiinNo,
        });

        // 印刷対象が見つからない場合は、エラーを表示しない
        if (response?.headers["content-type"] === "text/html") {
          const text = await response?.data?.text();

          if (text?.includes(NotFoundPrintText)) {
            return;
          }
        }

        await generatePdfDocument(response);
      } catch (error) {
        logger({
          error,
          message: `failed to generate drug note, raiinNo: ${raiinNo}`,
        });
        if (error instanceof ApolloError || error instanceof Error) {
          handleError({
            error,
            commonMessage: "お薬手帳シールの出力に失敗しました。",
          });
        }
      }
    });

    await Promise.all(requests);
  };

  const handleGenerateDocument = async (
    printConfig:
      | UseCaseAccountingGetAccountingSystemConfAccountingConfigDto
      | undefined,
    data: DomainModelsAccountDueSyunoSeikyuModel[],
    ptId: string,
    sinDate: number,
    isCalculateProcess: boolean = false,
  ) => {
    if (!printConfig) {
      return;
    }

    const {
      isCheckedPrintReceipt,
      isCheckedPrintDetail,
      isVisiblePrintOutDrg,
      isCheckedPrintOutDrg,
      isVisiblePrintDrgNote,
      isCheckedPrintDrgNote,
      isVisiblePrintDrgLabel,
      isCheckedPrintDrgLabel,
    } = printConfig;
    const listRaiinNo = data
      .filter((item) => item.raiinNo && Number(item.raiinNo) > 0)
      .map((item) => item.raiinNo) as string[];

    const listPaymentRaiinNo = data
      .filter(
        (item) =>
          Number(item?.raiinNo) > 0 &&
          item?.syunoNyukinModels?.length &&
          item?.syunoNyukinModels?.reduce((result, next) => {
            if (result?.sortNo && next?.sortNo) {
              return result?.sortNo > next?.sortNo ? result : next;
            }
            return result;
          })?.nyukinGaku !== 0,
      )
      .map((item) => item?.raiinNo) as string[];

    const requests: Promise<void>[] = [];

    if (isCheckedPrintReceipt || isCheckedPrintDetail) {
      requests.push(
        handleGenerateReceiptReport(
          ptId,
          listRaiinNo,
          listPaymentRaiinNo,
          printConfig,
          data,
          isCalculateProcess,
        ),
      );
    }

    if (isVisiblePrintOutDrg && isCheckedPrintOutDrg) {
      requests.push(handleGenerateOutDrug(ptId, sinDate, listRaiinNo));
    }

    if (isVisiblePrintDrgLabel && isCheckedPrintDrgLabel) {
      requests.push(handleGenerateDrgLabel(ptId, sinDate, listRaiinNo));
    }

    if (printConfig.isVisiblePrintDrgInf && printConfig.isCheckedPrintDrgInf) {
      requests.push(handleGenerateDrgInf(ptId, sinDate, listRaiinNo));
    }

    if (isVisiblePrintDrgNote && isCheckedPrintDrgNote) {
      requests.push(handleGenerateDrugNote(ptId, sinDate, listRaiinNo));
    }

    try {
      setIsGeneratingPdf(true);
      await Promise.all(requests);
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleGenerateReceiptPreview = async (
    ptId: string,
    hokenId: number,
    sinDate: number,
    isIncludeOutDrug: boolean,
  ) => {
    try {
      setIsGeneratingPdf(true);
      const response = await getReceiptPreview({
        ptId,
        hokenId,
        sinYm: Math.floor(sinDate / 100),
        isOpenedFromAccounting: true,
        isIncludeOutDrug,
      });

      await generatePdfDocument(response);
    } catch (error) {
      logger({ error, message: "failed to generate receipt preview" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "レセプトプレビューの出力に失敗しました。",
        });
      }
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  return {
    handleGenerateReceiptReport,
    handleGenerateOutDrug,
    handleGenerateDrgLabel,
    handleGenerateDrgInf,
    handleGenerateDrugNote,
    handleGenerateDocument,
    handleGenerateReceiptPreview,
    isPrinting: isGeneratingPdf,
  };
};
