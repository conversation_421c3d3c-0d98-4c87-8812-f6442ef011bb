import { createContext, useContext } from "react";

type Patient = {
  ptId: string;
  ptNum: string;
  sex: number;
  birthday: number;
  name: string;
  kanaName: string;
  portalCustomerId?: number;
};

export type AccountingQuery = {
  ptId: string;
  raiinNo: string;
  sinDate: number;
  patient: Patient;
  waitNumber: string;
  isOnlineReserve: boolean;
};

const AccountingPaymentContext = createContext<AccountingQuery | undefined>(
  undefined,
);

export const useAccountingQuery = (): AccountingQuery => {
  const context = useContext(AccountingPaymentContext);
  if (!context) {
    throw new Error(
      "useAccountingQuery must be used within an AccountingQueryProvider",
    );
  }
  return context;
};

type Props = {
  children: React.ReactNode;
  raiinNo: string;
  sinDate: number;
  waitNumber?: string;
  isOnlineReserve?: boolean;
  patient: Patient;
};

export const AccountingQueryProvider: React.FC<Props> = ({
  children,
  raiinNo,
  sinDate,
  waitNumber,
  isOnlineReserve = false,
  patient,
}) => {
  return (
    <AccountingPaymentContext.Provider
      value={{
        ptId: patient.ptId,
        raiinNo,
        sinDate,
        patient,
        waitNumber: waitNumber ?? "-",
        isOnlineReserve,
      }}
    >
      {children}
    </AccountingPaymentContext.Provider>
  );
};
