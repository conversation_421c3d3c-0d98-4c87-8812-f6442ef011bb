import type {
  EmrCloudApiResponsesAccountDueGetAccountDueListResponse,
  UseCaseAccountingGetAccountingSystemConfAccountingConfigDto,
} from "@/apis/gql/generated/types";
import type { GetApiAccountingGetListQuery } from "@/apis/gql/operations/__generated__/payment-detail";

export type OrderItem = {
  key: string;
  syosaisinDisplay: string;
  jikanDisplay: string;
  kaName: string;
  hokenGroups: HokenGroup[];
  raiinNo: string;
  hokenPid: number;
  treatmentDepartmentId: number;
  hokenTitle: string;
};

export type HokenGroup = {
  key: string;
  hokenPid: number;
  hokenTitle: string;
  groupOdrItems: GroupOrderItem[];
};

export type GroupOrderItem = {
  key: string;
  groupKouiCode: number;
  groupName: string;
  inOutKbn: number;
  inOutName: string;
  syohoSbt: number;
  sikyuKbn: number;
  tosekiKbn: number;
  santeiKbn: number;
  odrInfs: OrderInfo[];
};

export type OrderInfo = {
  key: string;
  inoutKbn: number;
  odrKouiKbn: number;
  rpName: string;
  sortNo: number;
  odrDetails: OrderDetail[];
  hokenPid?: number;
  hokenTitle?: string;
};

export type OrderDetail = {
  key: string;
  displayItemName: string;
  ten: number;
  isGetPriceInYakka: boolean;
  syohoKbn: number;
  yakka: number;
  unitName: string;
  sinKouiKbn: number;
  itemCd: string;
  drugKbn: number;
  termVal: number;
  suryo: number;
  itemName: string;
  bunkatu: string;
  cmtOpt: string;
  rowNo: number;
};

export type PatientDisease = {
  key: string;
  byomeiCd: string;
  fullByomei: string;
  startDate: string;
  tenKiBinding: string;
  tenkiDate: string;
  isMainDisease: boolean;
  isSuspect: boolean;
  sikkanKbn: number;
  nanbyoCd: number;
  hokenId: number;
};

export type PatientMedicalInfo = {
  key: string;
  sinIdBinding: string;
  asterisk: string;
  itemName: string;
  quantity: string;
  tenKai: string;
  totalBinding: string;
  isDivider: boolean;
  futanSBinding: string;
  futanK1Binding: string;
  futanK2Binding: string;
  futanK3Binding: string;
  futanK4Binding: string;
  enTenKbn: number;
};

export type PaymentMethod = {
  paymentMethodCd: number;
  payName: string;
  paySname: string;
  sortNo: number;
};

export type AccountingInfo = NonNullable<
  NonNullable<
    NonNullable<
      NonNullable<
        GetApiAccountingGetListQuery["getApiAccountingGetList"]
      >["data"]
    >
  >
> & {
  isFcoWaiting: boolean;
  isDisabledAccounting: boolean;
};

export type AccountingPayment = {
  ptId: number;
  sinDate: number;
  raiinNo: number;
  sumAdjust: number;
  sumAdjustView: number;
  thisWari: number;
  credit: number;
  payType: number;
  comment: string;
  isDisCharged: boolean;
  kaikeiTime: string;
  printOptions?: UseCaseAccountingGetAccountingSystemConfAccountingConfigDto;
};

export type RaiinNoSelection = {
  raiinNo: string;
  raiinBinding: string;
  patternName: string;
};

export type ObjectType = {
  [key: string]: number | string | boolean | undefined | null;
};

export type AccountDueList =
  EmrCloudApiResponsesAccountDueGetAccountDueListResponse & {
    fcoLabel: string;
  };

export interface IParamsAccountingReport {
  HpId: number;
  Mode: number;
  PtId: number;
  MultiAccountDueListModels: ISaveAccountDueListModel[];
  IsPrintMonth: boolean;
  Ryoshusho: boolean;
  Meisai: boolean;
}

export interface ISaveAccountDueListModel {
  SinDate: number;
  RaiinNo: number;
  OyaRaiinNo: number;
}
