import { useMemo } from "react";

import styled from "styled-components";

import {
  displayDateNumber,
  formatYYYYMMDDWithJapanese,
} from "@/utils/datetime-format";
import { Gender } from "@/constants/common";

import { getAgeDisplay } from "../../utils/accounting-detail";
import { RaiinNoAllSelection } from "../../constants/accounting";
import { useAccountingQuery } from "../../providers/AccountingQueryProvider";

import type { RaiinNoSelection } from "@/components/common/KartePayment/types/accounting-detail";
import type { DomainModelsPatientInforPatientInforModel } from "@/apis/gql/generated/types";

const Wrapper = styled.div`
  border-bottom: solid 1px #e2e3e5;
  display: flex;
  height: 56px;
`;

const Title = styled.div`
  font-size: 12px;
  line-height: 1;
`;

const Name = styled.div`
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
`;

const GenderWrapper = styled.div`
  display: flex;
  align-self: flex-end;
  padding-bottom: 8px;
`;

const StyledGender = styled.div<{
  $isMale: boolean;
}>`
  font-size: 12px;
  color: white;
  line-height: normal;
  background: ${(props) => (props.$isMale ? "#006ec3" : "#f48e91")};
  border-radius: 2px;
  margin-right: 7px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px 6px;
`;

const Age = styled.div`
  margin: 0px 8px;
`;

const NameWrapper = styled.div`
  display: flex;
  gap: 4px;
  flex-direction: column;
  margin: 0px 8px 0px 12px;
  justify-content: center;
  padding: 8px 0;
`;

const PatientType = styled.div`
  font-size: 11px;
  color: white;
  line-height: 1;
  background: #006ec3;
  border-radius: 2px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  align-self: center;
`;

const FlexDiv = styled.div`
  display: flex;
  border-right: solid 1px #e2e3e5;
  padding-right: 8px;
`;

const ReserveNumberWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 64px;
  height: 56px;
  padding: 0px 7px;
`;

const ReserveNumber = styled.div`
  font-size: 26px;
  font-weight: bold;
  line-height: 1;
`;

const PatientNumberWrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding: 8px 14px 10px;
  background: #eaf0f5;
  align-items: center;
`;

const PatientNumber = styled.div`
  font-size: 18px;
  line-height: normal;
`;

const TreatmentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 160px;
  align-items: center;
  justify-content: center;
  border-right: solid 1px #e2e3e5;
`;

const Time = styled.div`
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
`;

const RaiinNoPatternNameWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-left: 12px;
`;

const RaiinNoPatternName = styled.div`
  width: fit-content;
  height: fit-content;
  padding: 4px 8px;
  background-color: #eaf0f5;
`;

type Props = {
  patientInfo: DomainModelsPatientInforPatientInforModel | undefined;
  selectedRaiinNo: string;
  raiinNoList: RaiinNoSelection[];
};

export const PatientInfo: React.FC<Props> = ({
  patientInfo,
  selectedRaiinNo,
  raiinNoList,
}) => {
  const { sinDate, raiinNo, waitNumber } = useAccountingQuery();
  const raiinNoPatternName = useMemo(() => {
    const targetRaiinNo =
      selectedRaiinNo === RaiinNoAllSelection.raiinNo
        ? raiinNo
        : selectedRaiinNo;

    const targetRaiinNoInfo = raiinNoList.find(
      (item) => String(item.raiinNo) === targetRaiinNo,
    );

    return targetRaiinNoInfo?.patternName;
  }, [selectedRaiinNo, raiinNo, raiinNoList]);

  const isPortalCustomer = (patientInfo?.portalCustomerId ?? 0) > 0;

  return (
    <Wrapper>
      <TreatmentWrapper>
        <Time>{displayDateNumber(sinDate)}</Time>
      </TreatmentWrapper>
      <ReserveNumberWrapper>
        <Title>受付番号</Title>
        <ReserveNumber>{waitNumber}</ReserveNumber>
      </ReserveNumberWrapper>
      <PatientNumberWrapper>
        <Title>患者番号</Title>
        <PatientNumber>{patientInfo?.ptNum}</PatientNumber>
      </PatientNumberWrapper>
      <FlexDiv>
        <NameWrapper>
          <Title>{patientInfo?.kanaName}</Title>
          <Name>{patientInfo?.name}</Name>
        </NameWrapper>
        <GenderWrapper>
          <StyledGender $isMale={patientInfo?.sex === Gender.Male}>
            {patientInfo?.sex === Gender.Male ? "男性" : "女性"}
          </StyledGender>
          <div>
            {patientInfo?.birthday
              ? `${formatYYYYMMDDWithJapanese(patientInfo.birthday.toString())}生 `
              : " "}
          </div>
          <Age>{getAgeDisplay(patientInfo?.birthday, sinDate)}</Age>
          {isPortalCustomer && <PatientType>マップ会員</PatientType>}
        </GenderWrapper>
      </FlexDiv>
      <RaiinNoPatternNameWrapper>
        <RaiinNoPatternName>{raiinNoPatternName}</RaiinNoPatternName>
      </RaiinNoPatternNameWrapper>
    </Wrapper>
  );
};
