import styled from "styled-components";

import {
  NYUKINKBN,
} from "@/components/common/KartePayment/constants/accounting";
import { DropdownMenu } from "@/components/ui/DropdownMenu";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { SvgIconWarning } from "@/components/ui/Icon/IconWarning";
import { IconButton } from "@/components/ui/IconButton";
import { Table } from "@/components/ui/Table";
import { ModalLoading } from "@/components/ui/ModalLoading";

import { usePaymentListTable } from "../../hooks/usePaymentListTable";
import { useModal } from "../../providers/ModalProvider";
import { convertDateValue } from "../../utils/payment";

import { ExpandPayments } from "./ExpandPayments";

import type { EmrCloudApiResponsesAccountDueAccountDueDto } from "@/apis/gql/generated/types";
import type { TableColumnsType } from "antd";
import type { Dispatch, SetStateAction } from "react";
import type { AccountDueList } from "../../types/accounting-detail";

const StyledTable = styled(Table)<{ $hideBody?: boolean }>`
  table {
    border-collapse: collapse;
  }

  .ant-table-body {
    min-height: ${({ $hideBody }) => ($hideBody ? undefined : "400px")};
    background-color: #f1f4f7;

    .ant-table-row .ant-table-cell {
      padding: 12px 6px !important;
      height: 52px;
      background-color: white;
    }

    .ant-table-expanded-row .ant-table-cell {
      background: white;
      padding: 0 !important;
    }

    .ant-table-placeholder .ant-table-cell {
      display: ${({ $hideBody }) => ($hideBody ? "none" : undefined)};
    }
  }

  .expanded-row .ant-table-cell {
    border: none;
  }
`;

const NoDataWrapper = styled.div`
  background-color: #f1f4f7;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px 20px;
  color: #243544;
  font-size: 16px;
  margin: 0;
`;

const FlexEnd = styled.div`
  display: flex;
  justify-content: end;
`;

const Unpaid = styled(FlexEnd)<{ $isUnpaid: boolean }>`
  color: ${({ $isUnpaid }) => ($isUnpaid ? "red" : undefined)};
`;

const ConsultationDate = styled.div`
  color: #007aff;
  cursor: pointer;
`;

const WarningAmount = styled(FlexEnd)`
  align-items: center;
`;

const IconWarning = styled(SvgIconWarning)`
  margin-right: 8px;
`;

const MoreIcon = styled(DropdownMenu)`
  &.ant-btn.ant-btn-icon-only {
    margin: auto;
    width: 28px;
    height: 28px;
  }
`;

type Props = {
  accountDueList: AccountDueList;
  filterUnpaid: boolean;
  filterUncalculate: boolean;
  setTargetRaiinNo: Dispatch<SetStateAction<string>>;
};

export const PaymentListTable = ({
  accountDueList,
  filterUnpaid,
  setTargetRaiinNo,
  filterUncalculate,
}: Props) => {
  const { handleOpenModal } = useModal();
  const {
    exempt,
    deletePayment,
    paidList,
    redirectToPayment,
    handleExpandRowCondition,
    getTreatmentDepartmentTitle,
    handleShowPaymentMethod,
    savePaymentLoading,
    isFcoPayment,
  } = usePaymentListTable({
    accountDueList,
    filterUncalculate,
    filterUnpaid,
  });

  const columns = [
    {
      title: "診察日",
      width: 100,
      align: "center",
      render: (_, record) => (
        <ConsultationDate onClick={() => redirectToPayment(record)}>
          {record.sinDateDisplay}
        </ConsultationDate>
      ),
    },
    {
      title: "状態",
      width: 60,
      align: "center",
      render: (_, { stateDisplay }) => stateDisplay,
    },
    {
      title: "診療科",
      width: 200,
      align: "center",
      render: (_, { treatmentDepartmentId }) => {
        if (!treatmentDepartmentId) return "";
        return getTreatmentDepartmentTitle(treatmentDepartmentId);
      },
    },
    {
      title: "保険種類",
      width: 160,
      render: (_, { hokenPatternName }) => hokenPatternName,
    },
    {
      title: "請求金額",
      width: 120,
      render: (_, { newSeikyuGaku, seikyuGaku, raiinNo }) => (
        <WarningAmount>
          {seikyuGaku !== newSeikyuGaku && (
            <IconWarning
              onClick={() => {
                if (!raiinNo) return;
                setTargetRaiinNo(raiinNo);
                handleOpenModal("PAYMENT_LIST_PAID_ALERT");
              }}
            />
          )}{" "}
          {seikyuGaku?.toLocaleString()} 円
        </WarningAmount>
      ),
    },
    {
      title: "入金日",
      width: 100,
      align: "center",
      render: (_, { nyukinDate, nyukinKbn }) => {
        if (nyukinKbn === NYUKINKBN.EXEMPT) return "";
        return convertDateValue(nyukinDate);
      },
    },
    {
      title: "支払方法",
      align: "center",
      width: 120,
      render: (_, payment) => handleShowPaymentMethod(payment),
    },
    {
      title: "入金額",
      width: 120,
      render: (_, { nyukinGaku, nyukinKbn }) => {
        if (nyukinKbn === NYUKINKBN.EXEMPT) return "";
        return <FlexEnd>{nyukinGaku?.toLocaleString()} 円</FlexEnd>;
      },
    },
    {
      title: "調整額",
      width: 120,
      render: (_, { adjustFutan, nyukinKbn }) => {
        if (nyukinKbn === NYUKINKBN.EXEMPT) return "";
        return (
          <WarningAmount>{adjustFutan?.toLocaleString()} 円</WarningAmount>
        );
      },
    },
    {
      title: "未収金額",
      width: 120,
      render: (_, { unPaid, raiinNo, nyukinKbn }) => {
        if (nyukinKbn === NYUKINKBN.EXEMPT) return "";
        const isUnpaid =
          accountDueList.accountDueList?.filter((e) => e.raiinNo === raiinNo)
            .length === 1 && !!unPaid;
        return (
          <Unpaid $isUnpaid={isUnpaid}>{unPaid?.toLocaleString()} 円</Unpaid>
        );
      },
    },
    {
      title: "",
      width: 60,
      render: (_, payment) => (
        <MoreIcon
          trigger={["click"]}
          menu={{
            items: [
              {
                key: "EDIT",
                label: "編集",
                disabled: payment.nyukinKbn === NYUKINKBN.EXEMPT,
                onClick: () => {
                  if (!payment.raiinNo) return;
                  setTargetRaiinNo(payment.raiinNo);
                  handleOpenModal("PAYMENT_LIST_PAID_EDIT");
                },
              },
              {
                key: "EXEMPT",
                label: "免除",
                disabled:
                  payment.nyukinKbn !== NYUKINKBN.UNSETTLED ||
                  isFcoPayment(payment),
                onClick: () => exempt(payment),
              },
              {
                key: "DEL",
                label: "入金取り消し",
                disabled:
                  payment.nyukinKbn === NYUKINKBN.UNSETTLED &&
                  !isFcoPayment(payment),
                onClick: () => deletePayment(payment),
              },
            ],
          }}
        >
          <IconButton varient="square" icon={<SvgIconMore />} />
        </MoreIcon>
      ),
    },
  ] satisfies TableColumnsType<EmrCloudApiResponsesAccountDueAccountDueDto>;

  return (
    <>
      {savePaymentLoading && <ModalLoading />}
      <StyledTable
        columns={columns}
        rowClassName={({ raiinNo }) =>
          handleExpandRowCondition(raiinNo) ? "expanded-row" : ""
        }
        dataSource={paidList}
        rowKey="raiinNo"
        expandable={{
          expandedRowRender: ({ raiinNo }) => (
            <ExpandPayments raiinNo={raiinNo} accountDueList={accountDueList} />
          ),
          rowExpandable: ({ raiinNo }) => handleExpandRowCondition(raiinNo),
          showExpandColumn: false,
          defaultExpandAllRows: true,
        }}
        scroll={{ y: 400 }}
        $hideBody={paidList.length === 0}
      />
      {paidList.length === 0 && (
        <NoDataWrapper>会計情報はありません。</NoDataWrapper>
      )}
    </>
  );
};
