import { useCallback, useEffect, useState } from "react";

import { useRouter } from "next/router";
import { FormProvider, useForm } from "react-hook-form";
import styled from "styled-components";

import { useCheckAccounting } from "@/components/common/KartePayment/hooks/useCheckAccounting";
import { useGetAllAccountingDetailData } from "@/components/common/KartePayment/hooks/useGetAllAccountingDetailData";
import { usePrint } from "@/components/common/KartePayment/hooks/usePrint";
import { useSaveAccounting } from "@/components/common/KartePayment/hooks/useSaveAccounting";
import { useAccountingPayment } from "@/components/common/KartePayment/providers/AccountingPaymentProvider";
import { Modal } from "@/components/ui/Modal";
import { ModalLoading } from "@/components/ui/ModalLoading";
import { Button } from "@/components/ui/NewButton";
import { RenkeiCode } from "@/constants/renkei";
import { useBufferLoader } from "@/hooks/useBufferLoader";
import { useRenkei } from "@/hooks/useRenkei";

import { PaymentMethodCd } from "../../constants/accounting";
import { usePaymentAuditLog } from "../../hooks/usePaymentAuditLog";
import { useAccountingQuery } from "../../providers/AccountingQueryProvider";
import { useModal } from "../../providers/ModalProvider";
import { handleCheckingAccountStatus } from "../../utils/payment";
import { AccountingContainer } from "../detail/AccountingContainer";
import { PatientInfo } from "../detail/PatientInfo";

import { AccountingExemptionModal } from "./AccountingExemptionModal";
import { PaymentCompleteModal } from "./PaymentCompleteModal";
import { PaymentConfirmModal } from "./PaymentConfirmModal";
import { PaymentFailureModal } from "./PaymentFailureModal";

import type { AccountingPayment } from "@/components/common/KartePayment/types/accounting-detail";

const ButtonWrapper = styled.div`
  display: flex;
  width: 248px;
  justify-content: "space-between";
`;

const PaymentListButton = styled(Button)`
  margin-right: 8px;
`;

type Props = {
  isOpenModal: boolean;
  handleOpenPaymentModal: () => void;
  handleClosePaymentModal: () => void;
};

export const PaymentModal: React.FC<Props> = ({
  isOpenModal,
  handleOpenPaymentModal,
  handleClosePaymentModal,
}) => {
  const { handleOpenModal, handleCloseModal, handleCloseAllModal } = useModal();
  const { ptId, raiinNo, sinDate } = useAccountingQuery();
  const { accountingPayment, setAccountingPayment } = useAccountingPayment();
  const { isLoading: checkingAccounting, checkingPaymentAccounting } =
    useCheckAccounting();
  const { isLoading: savingAccounting, saveAccounting } = useSaveAccounting();
  const { isPrinting, handleGenerateDocument } = usePrint();
  const router = useRouter();
  const { addAccountingOpenLog, addAccountingCloseLog } = usePaymentAuditLog();

  const [isDisCharge, setIsDisCharge] = useState(false);

  const {
    loading: originLoading,
    examHistoryData,
    patientDiseaseData,
    patientMedicalInfoData,
    patientWarningMemoData,
    accountingInfoData,
    paymentMethodList,
    accountingPrintConf,
    raiinNoList,
    hokenSelectList,
    isIncludeOutDrug,
    patientInfo,
    handleReload,
    handleRecalculate,
    selectedRaiinNo,
    handleChangeRaiinNo,
    refetchAccountingInfo,
  } = useGetAllAccountingDetailData({
    ptId,
    raiinNo,
    sinDate,
    setAccountingPayment,
    isPaymentModalOpen: isOpenModal,
    accountingPayment,
  });

  const { loading } = useBufferLoader({ originLoading });

  const [checkingAccountingError, setCheckingAccountingError] = useState("");
  const { sendRenkei } = useRenkei();

  const method = useForm<AccountingPayment>({
    mode: "all",
  });

  const payment = async (isDisCharge: boolean = false) => {
    if (!accountingInfoData || !accountingPayment) {
      return;
    }

    // Reset error
    setCheckingAccountingError("");

    const result = await checkingPaymentAccounting(
      accountingInfoData,
      accountingPayment,
      ptId,
      sinDate,
      raiinNo,
      isDisCharge,
    );

    setIsDisCharge(isDisCharge);

    handleCheckingAccountStatus(
      result,
      async () => {
        await saveAccounting(accountingPayment, isDisCharge, async () => {
          const refetchAccountingInfoData = await refetchAccountingInfo();

          await handleGenerateDocument(
            method.getValues("printOptions"),
            refetchAccountingInfoData?.syunoSeikyuModels || [],
            ptId,
            sinDate,
            true,
          );

          handleCloseModal("PAYMENT_CONFIRM");
          handleOpenModal("PAYMENT_COMPLETE");
          setCheckingAccountingError("");

          await sendRenkei({
            ptId: Number(ptId),
            raiinNo: Number(raiinNo ?? 0),
            sinDate,
            eventCd: RenkeiCode.AccountingPayment,
            // nyukinDate,
          });

          // 会計の完了モダールが閉じられると、会計モーダルも自動的に閉じられるため、ページの再読み込みは不要です。
          // await handleReload();
        });
      },
      (message) => {
        setCheckingAccountingError(message);
        handleOpenModal("PAYMENT_ERROR");
        handleCloseModal("PAYMENT_CONFIRM");
      },
    );
  };

  const closePaymentFailureModal = useCallback(() => {
    handleCloseModal("PAYMENT_ERROR");

    if (!isDisCharge) {
      handleOpenModal("PAYMENT_CONFIRM");
    }
    // eslint-disable-next-line
  }, [isDisCharge]);

  useEffect(() => {
    const { isReady, query, replace, pathname } = router;
    if (!isReady || query.openPayment !== "true") return;
    handleOpenPaymentModal();
    addAccountingOpenLog();
    const newQuery = { ...query };
    delete newQuery.openPayment;
    replace({ pathname, query: newQuery }, undefined, { shallow: true });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router]);

  useEffect(() => {
    if (isOpenModal) addAccountingOpenLog();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpenModal]);

  return (
    <Modal
      isOpen={isOpenModal}
      title="会計"
      centered
      forceRender
      width={1440}
      footer={[
        <Button
          varient="tertiary"
          key="cancel"
          onClick={() => {
            handleCloseAllModal();
            handleClosePaymentModal();
            addAccountingCloseLog();
          }}
        >
          キャンセル
        </Button>,
        <ButtonWrapper key="commit">
          <PaymentListButton
            varient="standard"
            onClick={() => handleOpenModal("PAYMENT_LIST")}
          >
            会計一覧
          </PaymentListButton>
          <Button
            varient="secondary"
            onClick={() => handleOpenModal("PAYMENT_CONFIRM")}
            disabled={
              !method.formState.isValid ||
              accountingInfoData?.isDisabledAccounting
            }
          >
            確認画面へ
          </Button>
        </ButtonWrapper>,
      ]}
    >
      {isOpenModal && (
        <>
          <PatientInfo
            patientInfo={patientInfo}
            selectedRaiinNo={selectedRaiinNo}
            raiinNoList={raiinNoList}
          />
          <FormProvider {...method}>
            {loading && <ModalLoading />}
            <AccountingContainer
              ptId={ptId}
              sinDate={sinDate}
              accountingInfo={accountingInfoData}
              orders={examHistoryData}
              patientDiseases={patientDiseaseData}
              patientMedicalInfo={patientMedicalInfoData}
              patientWarningMemo={patientWarningMemoData}
              paymentMethodList={paymentMethodList}
              accountingPrintConf={accountingPrintConf}
              raiinNoList={raiinNoList}
              hokenSelectList={hokenSelectList}
              isIncludeOutDrug={isIncludeOutDrug}
              handleReload={handleReload}
              handleRecalculate={handleRecalculate}
              handleChangeRaiinNo={handleChangeRaiinNo}
              selectedRaiinNo={selectedRaiinNo}
            />
          </FormProvider>

          {accountingInfoData && accountingPayment && (
            <PaymentConfirmModal
              accountingPayment={accountingPayment}
              paymentMethodList={paymentMethodList}
              submitPayment={() => payment()}
              loading={checkingAccounting || savingAccounting || isPrinting}
            />
          )}
        </>
      )}

      <PaymentCompleteModal
        isFCOPayment={accountingPayment?.payType === PaymentMethodCd.FCO}
      />
      <PaymentFailureModal
        errorMessage={checkingAccountingError}
        closeModal={closePaymentFailureModal}
      />
      <AccountingExemptionModal submitPayment={() => payment(true)} />
    </Modal>
  );
};
